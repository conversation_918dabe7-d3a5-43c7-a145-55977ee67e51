@echo off
chcp 65001 >nul
title ChatDB 管理菜单

:MENU
cls
echo ========================================
echo        ChatDB 管理菜单 (Windows)
echo ========================================
echo.
echo 请选择操作:
echo.
echo  [1] 环境安装和配置
echo      └─ 安装Python、Node.js依赖
echo.
echo  [2] 数据库初始化
echo      └─ 初始化MySQL和混合检索系统
echo.
echo  [3] 启动ChatDB服务
echo      └─ 启动后端和前端服务
echo.
echo  [4] 停止ChatDB服务
echo      └─ 停止所有相关服务
echo.
echo  [5] 检查服务状态
echo      └─ 查看服务运行状态和健康检查
echo.
echo  [6] 运行测试套件
echo      └─ 执行单元测试和集成测试
echo.
echo  [7] 构建生产版本
echo      └─ 构建前端生产版本
echo.
echo  [8] 备份数据
echo      └─ 备份配置和日志文件
echo.
echo  [9] 项目完善工具
echo      └─ 清理文件、创建模板等
echo.
echo  [A] 打开应用 (浏览器)
echo      └─ 在浏览器中打开ChatDB
echo.
echo  [B] 查看日志文件
echo      └─ 打开日志文件查看器
echo.
echo  [C] 编辑配置文件
echo      └─ 编辑环境变量配置
echo.
echo  [D] 查看文档
echo      └─ 打开项目文档
echo.
echo  [0] 退出
echo.
echo ========================================
set /p choice=请输入选项 (1-9, A-D, 0): 

if "%choice%"=="1" goto SETUP
if "%choice%"=="2" goto INIT_DB
if "%choice%"=="3" goto START
if "%choice%"=="4" goto STOP
if "%choice%"=="5" goto STATUS
if "%choice%"=="6" goto TEST
if "%choice%"=="7" goto BUILD
if "%choice%"=="8" goto BACKUP
if "%choice%"=="9" goto COMPLETE
if /i "%choice%"=="A" goto OPEN_APP
if /i "%choice%"=="B" goto VIEW_LOGS
if /i "%choice%"=="C" goto EDIT_CONFIG
if /i "%choice%"=="D" goto VIEW_DOCS
if "%choice%"=="0" goto EXIT

echo.
echo [!] 无效选项，请重新选择
timeout /t 2 >nul
goto MENU

:SETUP
cls
echo [INFO] 正在运行环境安装脚本...
if exist "setup-environment.bat" (
    call setup-environment.bat
) else (
    echo [!] setup-environment.bat 不存在
    echo [INFO] 请手动安装环境依赖
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:INIT_DB
cls
echo [INFO] 正在初始化数据库...
if exist "init-database.bat" (
    call init-database.bat
) else (
    echo [!] 初始化脚本不存在，手动初始化数据库:
    echo.
    cd /d "%~dp0\backend"
    if exist "venv\Scripts\activate.bat" (
        call venv\Scripts\activate.bat
        echo [INFO] 初始化MySQL数据库...
        python init_db.py
        echo [INFO] 初始化混合检索系统...
        python init_hybrid_system.py
        echo [✓] 数据库初始化完成
    ) else (
        echo [!] Python虚拟环境未找到，请先运行环境安装
    )
    cd /d "%~dp0"
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:START
cls
echo [INFO] 正在启动ChatDB服务...
if exist "start-chatdb.bat" (
    call start-chatdb.bat
) else (
    echo [!] start-chatdb.bat 不存在
    echo [INFO] 请手动启动服务
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:STOP
cls
echo [INFO] 正在停止ChatDB服务...
if exist "stop-chatdb.bat" (
    call stop-chatdb.bat
) else (
    echo [!] stop-chatdb.bat 不存在
    echo [INFO] 手动停止服务:
    echo.
    echo 停止后端服务 (端口8000)...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000 ^| findstr LISTENING') do (
        echo 停止进程 %%a
        taskkill /pid %%a /f >nul 2>&1
    )
    echo 停止前端服务 (端口3000)...
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
        echo 停止进程 %%a
        taskkill /pid %%a /f >nul 2>&1
    )
    echo [✓] 服务停止完成
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:STATUS
cls
echo [INFO] 正在检查服务状态...
if exist "check-status.bat" (
    call check-status.bat
) else (
    echo [!] check-status.bat 不存在，手动检查状态:
    echo.
    echo 检查端口占用情况:
    netstat -an | findstr :8000 >nul 2>&1
    if %errorLevel% == 0 (
        echo [✓] 端口8000 (后端) - 正在使用
    ) else (
        echo [✗] 端口8000 (后端) - 未使用
    )
    netstat -an | findstr :3000 >nul 2>&1
    if %errorLevel% == 0 (
        echo [✓] 端口3000 (前端) - 正在使用
    ) else (
        echo [✗] 端口3000 (前端) - 未使用
    )
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:TEST
cls
echo [INFO] 正在运行测试套件...
if exist "run-tests.bat" (
    call run-tests.bat
) else (
    echo [!] 测试脚本不存在，手动运行测试:
    echo.
    cd /d "%~dp0\backend"
    if exist "venv\Scripts\activate.bat" (
        call venv\Scripts\activate.bat
        echo [INFO] 运行后端测试...
        python -m pytest tests/ -v
        echo [INFO] 运行应用测试...
        python -m pytest app/tests/ -v
    ) else (
        echo [!] Python虚拟环境未找到
    )
    cd /d "%~dp0\frontend"
    if exist "node_modules" (
        echo [INFO] 运行前端测试...
        npm test -- --watchAll=false
    ) else (
        echo [!] 前端依赖未安装
    )
    cd /d "%~dp0"
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:BUILD
cls
echo [INFO] 正在构建生产版本...
if exist "build-production.bat" (
    call build-production.bat
) else (
    echo [!] 构建脚本不存在，手动构建:
    echo.
    cd /d "%~dp0\frontend"
    if exist "node_modules" (
        echo [INFO] 构建前端生产版本...
        npm run build
        echo [✓] 前端构建完成，输出目录: build/
    ) else (
        echo [!] 前端依赖未安装，请先运行环境安装
    )
    cd /d "%~dp0"
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:BACKUP
cls
echo [INFO] 正在备份数据...
if exist "backup-data.bat" (
    call backup-data.bat
) else (
    echo [!] 备份脚本不存在，手动备份:
    echo.
    set BACKUP_DIR=backup\%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
    set BACKUP_DIR=%BACKUP_DIR: =0%
    mkdir "%BACKUP_DIR%" 2>nul
    echo [INFO] 备份目录: %BACKUP_DIR%
    if exist "backend\.env" (
        copy "backend\.env" "%BACKUP_DIR%\.env.backup" >nul 2>&1
        echo [✓] 配置文件已备份
    )
    if exist "logs" (
        xcopy "logs" "%BACKUP_DIR%\logs" /E /I /Q >nul 2>&1
        echo [✓] 日志文件已备份
    )
    echo [✓] 备份完成: %BACKUP_DIR%
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:COMPLETE
cls
echo [INFO] 正在运行项目完善工具...
if exist "complete-project.bat" (
    call complete-project.bat
) else (
    echo [!] complete-project.bat 不存在
    echo [INFO] 请手动运行项目完善操作
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:OPEN_APP
cls
echo [INFO] 正在打开ChatDB应用...
echo.
echo 检查服务状态...
netstat -an | findstr :3000 >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 前端服务正在运行
    start http://localhost:3000
) else (
    echo [!] 前端服务未运行
    echo [INFO] 是否启动服务? (Y/N)
    set /p start_choice=
    if /i "%start_choice%"=="Y" (
        echo [INFO] 启动服务中...
        start "启动服务" cmd /c "call start-chatdb.bat"
        timeout /t 15 /nobreak >nul
        start http://localhost:3000
    )
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:VIEW_LOGS
cls
echo [INFO] 查看日志文件...
echo.
if exist "logs" (
    echo 可用的日志文件:
    dir logs\*.log /b 2>nul
    echo.
    echo [1] 后端日志 (backend.log)
    echo [2] 前端日志 (frontend.log)
    echo [3] 打开日志目录
    echo [0] 返回主菜单
    echo.
    set /p log_choice=请选择: 
    
    if "%log_choice%"=="1" (
        if exist "logs\backend.log" (
            notepad logs\backend.log
        ) else (
            echo [!] 后端日志文件不存在
        )
    )
    if "%log_choice%"=="2" (
        if exist "logs\frontend.log" (
            notepad logs\frontend.log
        ) else (
            echo [!] 前端日志文件不存在
        )
    )
    if "%log_choice%"=="3" (
        explorer logs
    )
) else (
    echo [!] 日志目录不存在
    echo [INFO] 请先启动服务以生成日志文件
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:EDIT_CONFIG
cls
echo [INFO] 编辑配置文件...
echo.
if exist "backend\.env" (
    echo [INFO] 打开环境配置文件...
    notepad backend\.env
) else (
    echo [!] 配置文件不存在
    if exist "backend\.env.template" (
        echo [INFO] 发现模板文件，是否复制为配置文件? (Y/N)
        set /p copy_choice=
        if /i "%copy_choice%"=="Y" (
            copy "backend\.env.template" "backend\.env"
            echo [✓] 配置文件已创建，请编辑后保存
            notepad backend\.env
        )
    ) else (
        echo [!] 请先运行项目完善工具创建配置模板
    )
)
echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:VIEW_DOCS
cls
echo [INFO] 查看项目文档...
echo.
echo 可用文档:
if exist "README.md" echo [1] 项目说明 (README.md)
if exist "DEPLOYMENT.md" echo [2] 部署指南 (DEPLOYMENT.md)
if exist "backend\README.md" echo [3] 后端文档 (backend\README.md)
if exist "frontend\README.md" echo [4] 前端文档 (frontend\README.md)
echo [5] 在线API文档 (需要后端运行)
echo [0] 返回主菜单
echo.
set /p doc_choice=请选择: 

if "%doc_choice%"=="1" notepad README.md
if "%doc_choice%"=="2" notepad DEPLOYMENT.md
if "%doc_choice%"=="3" notepad backend\README.md
if "%doc_choice%"=="4" notepad frontend\README.md
if "%doc_choice%"=="5" start http://localhost:8000/docs

echo.
echo 按任意键返回主菜单...
pause >nul
goto MENU

:EXIT
cls
echo.
echo 感谢使用 ChatDB 管理菜单！
echo.
timeout /t 2 >nul
exit /b 0
