/* ReactFlow styles are now imported in SchemaManagementPage.tsx */

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.site-layout-content {
  min-height: 280px;
  padding: 24px;
  background: #fff;
}

.logo {
  float: left;
  width: 120px;
  height: 31px;
  margin: 0 24px 0 0;
  background: rgba(255, 255, 255, 0.3);
}

.ant-row-rtl .logo {
  float: right;
  margin: 0 0 0 24px;
}

.header {
  display: flex;
  align-items: center;
}

.ant-menu-horizontal {
  border-bottom: none;
}

.schema-canvas {
  width: 100%;
  height: 600px;
  background-color: #f0f2f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
}

.table-node {
  padding: 10px;
  border-radius: 3px;
  width: 200px;
  font-size: 12px;
  color: #222;
  text-align: left;
  border-width: 1px;
  border-style: solid;
  background: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.16);
}

.table-node .title {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

.table-node .column {
  margin: 2px 0;
  display: flex;
  justify-content: space-between;
}

.table-node .column .name {
  flex: 1;
}

.table-node .column .type {
  color: #888;
  font-size: 10px;
}

.table-node .column.primary-key {
  font-weight: bold;
}

.table-node .column.foreign-key {
  color: #1890ff;
}

.react-flow__edge-path {
  stroke: #b1b1b7;
  stroke-width: 2;
}

.react-flow__edge.selected .react-flow__edge-path {
  stroke: #1890ff;
}

.edgebutton {
  width: 20px;
  height: 20px;
  background: #eee;
  border: 1px solid #fff;
  cursor: pointer;
  border-radius: 50%;
  font-size: 12px;
  line-height: 1;
}
