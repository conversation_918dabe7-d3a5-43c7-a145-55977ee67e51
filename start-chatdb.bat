@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 一键启动脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 管理员权限检查通过
) else (
    echo [!] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo [INFO] 项目根目录: %PROJECT_ROOT%
echo.

:: 检查必要的软件
echo [INFO] 检查必要软件...

:: 检查Python
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Python 已安装
    python --version
) else (
    echo [✗] Python 未安装，请先安装 Python 3.9+
    echo     下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查Node.js
node --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Node.js 已安装
    node --version
) else (
    echo [✗] Node.js 未安装，请先安装 Node.js
    echo     下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查MySQL
mysql --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] MySQL 客户端已安装
) else (
    echo [!] MySQL 客户端未找到，请确保MySQL已安装并添加到PATH
)

echo.

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动服务函数
echo [INFO] 开始启动服务...
echo.

:: 1. 启动后端服务
echo [1/2] 启动后端服务...
cd /d "%PROJECT_ROOT%\backend"

:: 检查虚拟环境
if not exist "venv" (
    echo [INFO] 创建Python虚拟环境...
    python -m venv venv
    if %errorLevel% neq 0 (
        echo [✗] 虚拟环境创建失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo [INFO] 激活虚拟环境...
call venv\Scripts\activate.bat

:: 安装依赖
echo [INFO] 安装Python依赖...
pip install -r requirements.txt
if %errorLevel% neq 0 (
    echo [✗] Python依赖安装失败
    pause
    exit /b 1
)

:: 检查数据库连接
echo [INFO] 检查数据库连接...
python -c "
import os
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import settings
    print(f'MySQL: {settings.MYSQL_SERVER}:{settings.MYSQL_PORT}/{settings.MYSQL_DB}')
    print(f'Neo4j: {settings.NEO4J_URI}')
    print(f'Milvus: {settings.MILVUS_HOST}:{settings.MILVUS_PORT}')
    print('[✓] 配置文件加载成功')
except Exception as e:
    print(f'[✗] 配置文件加载失败: {e}')
    sys.exit(1)
"
if %errorLevel% neq 0 (
    echo [✗] 数据库配置检查失败
    pause
    exit /b 1
)

:: 初始化数据库（如果需要）
if not exist "database_initialized.flag" (
    echo [INFO] 初始化数据库...
    python init_db.py
    if %errorLevel% == 0 (
        echo [✓] 数据库初始化成功
        echo. > database_initialized.flag
    ) else (
        echo [!] 数据库初始化失败，请检查数据库连接
    )
)

:: 启动后端服务（后台运行）
echo [INFO] 启动FastAPI后端服务...
start "ChatDB Backend" cmd /c "cd /d \"%PROJECT_ROOT%\backend\" && call venv\Scripts\activate.bat && python main.py > ..\logs\backend.log 2>&1"

:: 等待后端启动
echo [INFO] 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 检查后端是否启动成功
powershell -Command "try { Invoke-RestMethod -Uri 'http://localhost:8000/api/v1/health' -TimeoutSec 5 } catch { exit 1 }" >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 后端服务启动成功 (http://localhost:8000)
) else (
    echo [!] 后端服务可能启动失败，请检查日志: logs\backend.log
)

:: 2. 启动前端服务
echo.
echo [2/2] 启动前端服务...
cd /d "%PROJECT_ROOT%\frontend"

:: 安装前端依赖
if not exist "node_modules" (
    echo [INFO] 安装前端依赖...
    npm install
    if %errorLevel% neq 0 (
        echo [✗] 前端依赖安装失败
        pause
        exit /b 1
    )
)

:: 启动前端服务（后台运行）
echo [INFO] 启动React前端服务...
start "ChatDB Frontend" cmd /c "cd /d \"%PROJECT_ROOT%\frontend\" && npm start > ..\logs\frontend.log 2>&1"

:: 等待前端启动
echo [INFO] 等待前端服务启动...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 服务地址:
echo   前端应用: http://localhost:3000
echo   后端API:  http://localhost:8000
echo   API文档:  http://localhost:8000/docs
echo.
echo 日志文件:
echo   后端日志: logs\backend.log
echo   前端日志: logs\frontend.log
echo.
echo 按任意键打开前端应用...
pause >nul

:: 打开浏览器
start http://localhost:3000

echo.
echo [INFO] 应用已启动，浏览器将自动打开
echo [INFO] 如需停止服务，请关闭相应的命令行窗口
echo.
pause
