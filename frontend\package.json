{"name": "chatdb-frontend", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^5.0.1", "@ant-design/x": "^1.3.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/markdown-it": "^14.1.2", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "antd": "^5.2.2", "axios": "^1.3.4", "chatdb-frontend": "file:", "date-fns": "^2.30.0", "markdown-it": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "reactflow": "^11.11.4", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-syntax-highlighter": "^15.5.6"}}