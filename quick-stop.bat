@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 快速停止脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0C

echo [INFO] 正在停止 ChatDB 相关服务...
echo.

:: 停止后端服务 (端口8000)
echo [1/2] 停止后端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000 ^| findstr LISTENING') do (
    echo [INFO] 停止占用8000端口的进程: %%a
    taskkill /pid %%a /f >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] 后端进程已停止
    )
)

:: 停止前端服务 (端口3000)
echo [2/2] 停止前端服务...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
    echo [INFO] 停止占用3000端口的进程: %%a
    taskkill /pid %%a /f >nul 2>&1
    if !errorLevel! == 0 (
        echo [✓] 前端进程已停止
    )
)

:: 停止命名窗口
echo.
echo [INFO] 停止命名窗口...
tasklist /fi "windowtitle eq ChatDB Backend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq ChatDB Backend*" /fo table /nh 2^>nul') do (
        if not "%%i"=="INFO:" (
            echo [INFO] 停止后端窗口进程: %%i
            taskkill /pid %%i /f >nul 2>&1
        )
    )
)

tasklist /fi "windowtitle eq ChatDB Frontend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul 2>&1
if %errorLevel% == 0 (
    for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq ChatDB Frontend*" /fo table /nh 2^>nul') do (
        if not "%%i"=="INFO:" (
            echo [INFO] 停止前端窗口进程: %%i
            taskkill /pid %%i /f >nul 2>&1
        )
    )
)

echo.
echo [✓] 服务停止完成
echo.

:: 检查端口是否已释放
echo [INFO] 检查端口状态...
netstat -an | findstr :8000 >nul 2>&1
if %errorLevel% == 0 (
    echo [!] 端口8000仍被占用
) else (
    echo [✓] 端口8000已释放
)

netstat -an | findstr :3000 >nul 2>&1
if %errorLevel% == 0 (
    echo [!] 端口3000仍被占用
) else (
    echo [✓] 端口3000已释放
)

echo.
echo ========================================
echo           停止完成！
echo ========================================
echo.
pause
