@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 项目完善脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0D

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 管理员权限检查通过
) else (
    echo [!] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo [INFO] 项目根目录: %PROJECT_ROOT%
echo [INFO] 开始完善项目...
echo.

:: 1. 清理调试文件
echo [1/8] 清理调试文件...
if exist "frontend\find-motion.js" (
    del "frontend\find-motion.js"
    echo [✓] 删除 find-motion.js
)
if exist "frontend\count-tags.js" (
    del "frontend\count-tags.js"
    echo [✓] 删除 count-tags.js
)

:: 2. 创建环境变量模板
echo.
echo [2/8] 创建环境变量模板...
if not exist "backend\.env.template" (
    echo # ChatDB 环境变量配置模板 > "backend\.env.template"
    echo # 复制此文件为 .env 并填入实际值 >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # OpenAI配置 >> "backend\.env.template"
    echo OPENAI_API_KEY=your_openai_api_key_here >> "backend\.env.template"
    echo OPENAI_API_BASE=https://api.deepseek.com/v1 >> "backend\.env.template"
    echo LLM_MODEL=deepseek-chat >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # MySQL配置 >> "backend\.env.template"
    echo MYSQL_SERVER=localhost >> "backend\.env.template"
    echo MYSQL_PORT=3306 >> "backend\.env.template"
    echo MYSQL_USER=root >> "backend\.env.template"
    echo MYSQL_PASSWORD=your_mysql_password >> "backend\.env.template"
    echo MYSQL_DB=chatdb >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # Neo4j配置 >> "backend\.env.template"
    echo NEO4J_URI=bolt://localhost:7687 >> "backend\.env.template"
    echo NEO4J_USER=neo4j >> "backend\.env.template"
    echo NEO4J_PASSWORD=your_neo4j_password >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # Milvus配置 >> "backend\.env.template"
    echo MILVUS_HOST=localhost >> "backend\.env.template"
    echo MILVUS_PORT=19530 >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # 向量模型配置 >> "backend\.env.template"
    echo EMBEDDING_MODEL=BAAI/bge-small-zh-v1.5 >> "backend\.env.template"
    echo VECTOR_DIMENSION=512 >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # 混合检索配置 >> "backend\.env.template"
    echo HYBRID_RETRIEVAL_ENABLED=true >> "backend\.env.template"
    echo SEMANTIC_WEIGHT=0.35 >> "backend\.env.template"
    echo STRUCTURAL_WEIGHT=0.35 >> "backend\.env.template"
    echo PATTERN_WEIGHT=0.20 >> "backend\.env.template"
    echo QUALITY_WEIGHT=0.10 >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # 学习配置 >> "backend\.env.template"
    echo AUTO_LEARNING_ENABLED=true >> "backend\.env.template"
    echo FEEDBACK_LEARNING_ENABLED=true >> "backend\.env.template"
    echo PATTERN_DISCOVERY_ENABLED=true >> "backend\.env.template"
    echo. >> "backend\.env.template"
    echo # 性能配置 >> "backend\.env.template"
    echo RETRIEVAL_CACHE_TTL=3600 >> "backend\.env.template"
    echo MAX_EXAMPLES_PER_QUERY=5 >> "backend\.env.template"
    echo PARALLEL_RETRIEVAL=true >> "backend\.env.template"
    
    echo [✓] 创建环境变量模板文件
)

:: 3. 创建数据库初始化脚本
echo.
echo [3/8] 创建数据库初始化脚本...
if not exist "init-database.bat" (
    echo @echo off > "init-database.bat"
    echo chcp 65001 ^>nul >> "init-database.bat"
    echo echo 初始化ChatDB数据库... >> "init-database.bat"
    echo echo. >> "init-database.bat"
    echo cd /d "%%~dp0\backend" >> "init-database.bat"
    echo if exist "venv\Scripts\activate.bat" ^( >> "init-database.bat"
    echo     call venv\Scripts\activate.bat >> "init-database.bat"
    echo     echo [INFO] 初始化MySQL数据库... >> "init-database.bat"
    echo     python init_db.py >> "init-database.bat"
    echo     echo [INFO] 初始化混合检索系统... >> "init-database.bat"
    echo     python init_hybrid_system.py >> "init-database.bat"
    echo     echo [✓] 数据库初始化完成 >> "init-database.bat"
    echo ^) else ^( >> "init-database.bat"
    echo     echo [✗] Python虚拟环境未找到，请先运行 setup-environment.bat >> "init-database.bat"
    echo ^) >> "init-database.bat"
    echo pause >> "init-database.bat"
    
    echo [✓] 创建数据库初始化脚本
)

:: 4. 创建测试运行脚本
echo.
echo [4/8] 创建测试运行脚本...
if not exist "run-tests.bat" (
    echo @echo off > "run-tests.bat"
    echo chcp 65001 ^>nul >> "run-tests.bat"
    echo echo 运行ChatDB测试套件... >> "run-tests.bat"
    echo echo. >> "run-tests.bat"
    echo cd /d "%%~dp0\backend" >> "run-tests.bat"
    echo if exist "venv\Scripts\activate.bat" ^( >> "run-tests.bat"
    echo     call venv\Scripts\activate.bat >> "run-tests.bat"
    echo     echo [INFO] 运行后端测试... >> "run-tests.bat"
    echo     python -m pytest tests/ -v >> "run-tests.bat"
    echo     echo [INFO] 运行应用测试... >> "run-tests.bat"
    echo     python -m pytest app/tests/ -v >> "run-tests.bat"
    echo ^) else ^( >> "run-tests.bat"
    echo     echo [✗] Python虚拟环境未找到，请先运行 setup-environment.bat >> "run-tests.bat"
    echo ^) >> "run-tests.bat"
    echo echo. >> "run-tests.bat"
    echo cd /d "%%~dp0\frontend" >> "run-tests.bat"
    echo if exist "node_modules" ^( >> "run-tests.bat"
    echo     echo [INFO] 运行前端测试... >> "run-tests.bat"
    echo     npm test -- --watchAll=false >> "run-tests.bat"
    echo ^) else ^( >> "run-tests.bat"
    echo     echo [✗] 前端依赖未安装，请先运行 setup-environment.bat >> "run-tests.bat"
    echo ^) >> "run-tests.bat"
    echo pause >> "run-tests.bat"
    
    echo [✓] 创建测试运行脚本
)

:: 5. 创建生产构建脚本
echo.
echo [5/8] 创建生产构建脚本...
if not exist "build-production.bat" (
    echo @echo off > "build-production.bat"
    echo chcp 65001 ^>nul >> "build-production.bat"
    echo echo 构建ChatDB生产版本... >> "build-production.bat"
    echo echo. >> "build-production.bat"
    echo cd /d "%%~dp0\frontend" >> "build-production.bat"
    echo if exist "node_modules" ^( >> "build-production.bat"
    echo     echo [INFO] 构建前端生产版本... >> "build-production.bat"
    echo     npm run build >> "build-production.bat"
    echo     echo [✓] 前端构建完成，输出目录: build/ >> "build-production.bat"
    echo ^) else ^( >> "build-production.bat"
    echo     echo [✗] 前端依赖未安装，请先运行 setup-environment.bat >> "build-production.bat"
    echo ^) >> "build-production.bat"
    echo echo. >> "build-production.bat"
    echo echo [INFO] 后端生产部署说明: >> "build-production.bat"
    echo echo   1. 使用 gunicorn 或 uvicorn 部署后端 >> "build-production.bat"
    echo echo   2. 配置 Nginx 反向代理 >> "build-production.bat"
    echo echo   3. 设置生产环境变量 >> "build-production.bat"
    echo pause >> "build-production.bat"
    
    echo [✓] 创建生产构建脚本
)

:: 6. 创建备份脚本
echo.
echo [6/8] 创建备份脚本...
if not exist "backup-data.bat" (
    echo @echo off > "backup-data.bat"
    echo chcp 65001 ^>nul >> "backup-data.bat"
    echo echo 备份ChatDB数据... >> "backup-data.bat"
    echo echo. >> "backup-data.bat"
    echo set BACKUP_DIR=backup\%%date:~0,4%%%%date:~5,2%%%%date:~8,2%%_%%time:~0,2%%%%time:~3,2%%%%time:~6,2%% >> "backup-data.bat"
    echo set BACKUP_DIR=%%BACKUP_DIR: =0%% >> "backup-data.bat"
    echo mkdir "%%BACKUP_DIR%%" 2^>nul >> "backup-data.bat"
    echo echo [INFO] 备份目录: %%BACKUP_DIR%% >> "backup-data.bat"
    echo echo [INFO] 备份配置文件... >> "backup-data.bat"
    echo copy "backend\.env" "%%BACKUP_DIR%%\.env.backup" ^>nul 2^>^&1 >> "backup-data.bat"
    echo echo [INFO] 备份日志文件... >> "backup-data.bat"
    echo xcopy "logs" "%%BACKUP_DIR%%\logs" /E /I /Q ^>nul 2^>^&1 >> "backup-data.bat"
    echo echo [✓] 数据备份完成: %%BACKUP_DIR%% >> "backup-data.bat"
    echo echo [INFO] 注意：数据库数据需要单独备份 >> "backup-data.bat"
    echo pause >> "backup-data.bat"
    
    echo [✓] 创建备份脚本
)

:: 7. 更新数据库迁移
echo.
echo [7/8] 检查数据库迁移...
cd /d "%PROJECT_ROOT%\backend"
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    echo [INFO] 检查Alembic迁移状态...
    alembic current 2>nul
    if %errorLevel% neq 0 (
        echo [INFO] 初始化Alembic迁移...
        alembic stamp head
    )
    echo [✓] 数据库迁移检查完成
) else (
    echo [!] Python虚拟环境未找到，跳过数据库迁移检查
)

:: 8. 创建项目文档
echo.
echo [8/8] 创建项目文档...
cd /d "%PROJECT_ROOT%"
if not exist "DEPLOYMENT.md" (
    echo # ChatDB 部署指南 > "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo ## Windows 环境部署 >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo ### 1. 环境准备 >> "DEPLOYMENT.md"
    echo - Python 3.9+ >> "DEPLOYMENT.md"
    echo - Node.js 16+ >> "DEPLOYMENT.md"
    echo - MySQL 8.0+ >> "DEPLOYMENT.md"
    echo - Neo4j 5.0+ >> "DEPLOYMENT.md"
    echo - Milvus 2.3+ >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo ### 2. 快速启动 >> "DEPLOYMENT.md"
    echo ```bash >> "DEPLOYMENT.md"
    echo # 1. 安装环境 >> "DEPLOYMENT.md"
    echo setup-environment.bat >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo # 2. 初始化数据库 >> "DEPLOYMENT.md"
    echo init-database.bat >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo # 3. 启动服务 >> "DEPLOYMENT.md"
    echo start-chatdb.bat >> "DEPLOYMENT.md"
    echo ``` >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo ### 3. 生产环境部署 >> "DEPLOYMENT.md"
    echo ```bash >> "DEPLOYMENT.md"
    echo # 构建生产版本 >> "DEPLOYMENT.md"
    echo build-production.bat >> "DEPLOYMENT.md"
    echo ``` >> "DEPLOYMENT.md"
    echo. >> "DEPLOYMENT.md"
    echo ### 4. 维护操作 >> "DEPLOYMENT.md"
    echo - 检查状态: `check-status.bat` >> "DEPLOYMENT.md"
    echo - 停止服务: `stop-chatdb.bat` >> "DEPLOYMENT.md"
    echo - 运行测试: `run-tests.bat` >> "DEPLOYMENT.md"
    echo - 备份数据: `backup-data.bat` >> "DEPLOYMENT.md"
    
    echo [✓] 创建部署文档
)

:: 创建快捷方式菜单
echo.
echo [INFO] 更新桌面快捷方式...
set DESKTOP=%USERPROFILE%\Desktop
if exist "%DESKTOP%" (
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\ChatDB管理菜单.lnk'); $Shortcut.TargetPath = '%PROJECT_ROOT%show-menu.bat'; $Shortcut.WorkingDirectory = '%PROJECT_ROOT%'; $Shortcut.IconLocation = 'shell32.dll,21'; $Shortcut.Save()"
    echo [✓] 创建管理菜单快捷方式
)

echo.
echo ========================================
echo           项目完善完成！
echo ========================================
echo.
echo 新增功能:
echo   [✓] 环境变量模板文件
echo   [✓] 数据库初始化脚本
echo   [✓] 测试运行脚本
echo   [✓] 生产构建脚本
echo   [✓] 数据备份脚本
echo   [✓] 部署文档
echo   [✓] 管理菜单快捷方式
echo.
echo 清理完成:
echo   [✓] 删除调试文件
echo   [✓] 检查数据库迁移
echo.
echo 下一步建议:
echo   1. 检查并更新 backend\.env 配置
echo   2. 运行 init-database.bat 初始化数据库
echo   3. 运行 run-tests.bat 验证功能
echo   4. 使用 start-chatdb.bat 启动应用
echo.
pause
