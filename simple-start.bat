@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title ChatDB 简单启动脚本

echo ========================================
echo    ChatDB 简单启动脚本 (Windows)
echo ========================================
echo.

:: 设置项目根目录
set "PROJECT_ROOT=%~dp0"
cd /d "%PROJECT_ROOT%"

echo [INFO] 项目目录: %PROJECT_ROOT%
echo.

:: 检查Python
echo [INFO] 检查Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未添加到PATH
    echo 请安装Python 3.9+: https://www.python.org/downloads/
    pause
    exit /b 1
) else (
    echo [OK] Python已安装
)

:: 检查Node.js
echo [INFO] 检查Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js未安装或未添加到PATH
    echo 请安装Node.js: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo [OK] Node.js已安装
)

:: 创建日志目录
if not exist "logs" mkdir "logs"

:: 后端设置
echo.
echo [INFO] 设置后端环境...
cd /d "%PROJECT_ROOT%backend"

:: 创建虚拟环境
if not exist "venv" (
    echo [INFO] 创建Python虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] 虚拟环境创建失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境并安装依赖
echo [INFO] 激活虚拟环境...
call "venv\Scripts\activate.bat"
if errorlevel 1 (
    echo [ERROR] 虚拟环境激活失败
    pause
    exit /b 1
)

:: 检查是否需要安装依赖
pip show fastapi >nul 2>&1
if errorlevel 1 (
    echo [INFO] 安装Python依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] 依赖安装失败
        pause
        exit /b 1
    )
)

:: 前端设置
echo.
echo [INFO] 设置前端环境...
cd /d "%PROJECT_ROOT%frontend"

if not exist "node_modules" (
    echo [INFO] 安装前端依赖...
    npm install
    if errorlevel 1 (
        echo [ERROR] 前端依赖安装失败
        pause
        exit /b 1
    )
)

:: 检查配置文件
echo.
echo [INFO] 检查配置文件...
cd /d "%PROJECT_ROOT%"

if not exist "backend\.env" (
    echo [WARN] 配置文件不存在，创建默认配置...
    (
        echo # ChatDB 配置文件
        echo OPENAI_API_KEY=your_api_key_here
        echo MYSQL_SERVER=localhost
        echo MYSQL_PORT=3306
        echo MYSQL_USER=root
        echo MYSQL_PASSWORD=mysql
        echo MYSQL_DB=chatdb
        echo NEO4J_URI=bolt://localhost:7687
        echo NEO4J_USER=neo4j
        echo NEO4J_PASSWORD=password
        echo MILVUS_HOST=localhost
        echo MILVUS_PORT=19530
    ) > "backend\.env"
    echo [WARN] 请编辑 backend\.env 文件设置正确的配置
)

:: 启动后端
echo.
echo [INFO] 启动后端服务...
cd /d "%PROJECT_ROOT%backend"
start "ChatDB-Backend" cmd /c "call venv\Scripts\activate.bat && python main.py"

:: 等待后端启动
echo [INFO] 等待后端启动...
timeout /t 8 /nobreak >nul

:: 启动前端
echo [INFO] 启动前端服务...
cd /d "%PROJECT_ROOT%frontend"
start "ChatDB-Frontend" cmd /c "npm start"

:: 等待前端启动
echo [INFO] 等待前端启动...
timeout /t 15 /nobreak >nul

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 服务地址:
echo   前端: http://localhost:3000
echo   后端: http://localhost:8000
echo   API文档: http://localhost:8000/docs
echo.
echo 按任意键打开前端...
pause >nul

start http://localhost:3000

echo.
echo 服务已启动，按任意键退出...
pause >nul
