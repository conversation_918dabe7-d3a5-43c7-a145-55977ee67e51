@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 服务状态检查 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0E

:: 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo [INFO] 检查时间: %date% %time%
echo [INFO] 项目目录: %PROJECT_ROOT%
echo.

:: 检查端口占用情况
echo [1/5] 检查端口占用情况...
echo.

:: 检查8000端口（后端）
netstat -an | findstr :8000 >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 端口8000 (后端) - 正在使用
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000 ^| findstr LISTENING') do (
        for /f "tokens=1" %%b in ('tasklist /fi "pid eq %%a" /fo table /nh 2^>nul') do (
            echo     进程: %%b (PID: %%a)
        )
    )
) else (
    echo [✗] 端口8000 (后端) - 未使用
)

:: 检查3000端口（前端）
netstat -an | findstr :3000 >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 端口3000 (前端) - 正在使用
    for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
        for /f "tokens=1" %%b in ('tasklist /fi "pid eq %%a" /fo table /nh 2^>nul') do (
            echo     进程: %%b (PID: %%a)
        )
    )
) else (
    echo [✗] 端口3000 (前端) - 未使用
)

:: 检查服务进程
echo.
echo [2/5] 检查服务进程...
echo.

:: 检查后端进程
tasklist /fi "windowtitle eq ChatDB Backend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 后端进程运行中:
    for /f "tokens=1,2" %%a in ('tasklist /fi "windowtitle eq ChatDB Backend*" /fo table /nh 2^>nul') do (
        if not "%%a"=="INFO:" echo     %%a (PID: %%b)
    )
) else (
    echo [✗] 后端进程未运行
)

:: 检查前端进程
tasklist /fi "windowtitle eq ChatDB Frontend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 前端进程运行中:
    for /f "tokens=1,2" %%a in ('tasklist /fi "windowtitle eq ChatDB Frontend*" /fo table /nh 2^>nul') do (
        if not "%%a"=="INFO:" echo     %%a (PID: %%b)
    )
) else (
    echo [✗] 前端进程未运行
)

:: 检查HTTP服务可用性
echo.
echo [3/5] 检查HTTP服务可用性...
echo.

:: 检查后端API
echo [INFO] 检查后端API (http://localhost:8000)...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8000/docs' -TimeoutSec 5; Write-Host '[✓] 后端API服务正常' } catch { Write-Host '[✗] 后端API服务异常:' $_.Exception.Message }" 2>nul

:: 检查前端服务
echo [INFO] 检查前端服务 (http://localhost:3000)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5; Write-Host '[✓] 前端服务正常' } catch { Write-Host '[✗] 前端服务异常:' $_.Exception.Message }" 2>nul

:: 检查依赖服务
echo.
echo [4/5] 检查依赖服务...
echo.

:: 检查MySQL连接
echo [INFO] 检查MySQL连接...
cd /d "%PROJECT_ROOT%\backend"
if exist "venv\Scripts\activate.bat" (
    call venv\Scripts\activate.bat
    python -c "
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import settings
    import pymysql
    conn = pymysql.connect(
        host=settings.MYSQL_SERVER,
        port=int(settings.MYSQL_PORT),
        user=settings.MYSQL_USER,
        password=settings.MYSQL_PASSWORD,
        database=settings.MYSQL_DB,
        connect_timeout=5
    )
    conn.close()
    print('[✓] MySQL连接正常')
except Exception as e:
    print(f'[✗] MySQL连接失败: {e}')
" 2>nul
) else (
    echo [!] Python虚拟环境未找到
)

:: 检查Neo4j连接
echo [INFO] 检查Neo4j连接...
if exist "venv\Scripts\activate.bat" (
    python -c "
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import settings
    from neo4j import GraphDatabase
    driver = GraphDatabase.driver(settings.NEO4J_URI, auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD))
    with driver.session() as session:
        result = session.run('RETURN 1')
        result.single()
    driver.close()
    print('[✓] Neo4j连接正常')
except Exception as e:
    print(f'[✗] Neo4j连接失败: {e}')
" 2>nul
)

:: 检查Milvus连接
echo [INFO] 检查Milvus连接...
if exist "venv\Scripts\activate.bat" (
    python -c "
import sys
sys.path.insert(0, '.')
try:
    from app.core.config import settings
    from pymilvus import connections
    connections.connect(
        alias='default',
        host=settings.MILVUS_HOST,
        port=settings.MILVUS_PORT,
        timeout=5
    )
    print('[✓] Milvus连接正常')
except Exception as e:
    print(f'[✗] Milvus连接失败: {e}')
" 2>nul
)

:: 检查日志文件
echo.
echo [5/5] 检查日志文件...
echo.

if exist "logs\backend.log" (
    echo [✓] 后端日志文件存在
    for %%A in ("logs\backend.log") do echo     大小: %%~zA 字节
    echo     最新几行:
    powershell -Command "Get-Content 'logs\backend.log' -Tail 3 | ForEach-Object { Write-Host '     ' $_ }" 2>nul
) else (
    echo [!] 后端日志文件不存在
)

echo.
if exist "logs\frontend.log" (
    echo [✓] 前端日志文件存在
    for %%A in ("logs\frontend.log") do echo     大小: %%~zA 字节
    echo     最新几行:
    powershell -Command "Get-Content 'logs\frontend.log' -Tail 3 | ForEach-Object { Write-Host '     ' $_ }" 2>nul
) else (
    echo [!] 前端日志文件不存在
)

:: 总结
echo.
echo ========================================
echo           状态检查完成
echo ========================================
echo.
echo 快速操作:
echo   1. 启动服务: start-chatdb.bat
echo   2. 停止服务: stop-chatdb.bat
echo   3. 查看日志: notepad logs\backend.log
echo   4. 打开应用: http://localhost:3000
echo.
pause
