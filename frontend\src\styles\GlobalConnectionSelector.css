.global-connection-selector {
  margin-left: auto;
  margin-right: 16px;
}

.global-connection-selector .connection-select {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.global-connection-selector .ant-select-selector {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  color: white !important;
}

.global-connection-selector .ant-select-selection-placeholder,
.global-connection-selector .ant-select-selection-item,
.global-connection-selector .ant-select-arrow {
  color: rgba(255, 255, 255, 0.85) !important;
}

.global-connection-selector .ant-select:hover .ant-select-selector {
  background: rgba(255, 255, 255, 0.05) !important;
}

.global-connection-selector .ant-select-focused .ant-select-selector {
  background: rgba(255, 255, 255, 0.15) !important;
}

/* 暗色下拉菜单 */
.connection-select-dropdown.ant-select-dropdown {
  background-color: #001529;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.48), 0 6px 16px 0 rgba(0, 0, 0, 0.32), 0 9px 28px 8px rgba(0, 0, 0, 0.2);
}

.connection-select-dropdown .ant-select-item {
  color: rgba(255, 255, 255, 0.85);
}

.connection-select-dropdown .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: rgba(255, 255, 255, 0.08);
}

.connection-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgba(24, 144, 255, 0.2);
  color: #1890ff;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .global-connection-selector {
    margin-right: 8px;
  }
  
  .global-connection-selector .ant-select {
    width: 150px !important;
  }
}
