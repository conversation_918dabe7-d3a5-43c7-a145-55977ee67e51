/* 时间轴聊天样式 */

.timeline-chat {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.timeline-container {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
  position: relative;
}

.timeline-container::-webkit-scrollbar {
  width: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background: transparent;
}

.timeline-container::-webkit-scrollbar-thumb {
  background-color: rgba(203, 213, 225, 0.5);
  border-radius: 3px;
}

/* 时间轴项目 */
.timeline-item {
  display: flex;
  gap: 20px;
  margin-bottom: 32px;
  position: relative;
  animation: fadeInUp 0.5s ease-out;
}

.timeline-item.user {
  flex-direction: row-reverse;
}

.timeline-item.user .timeline-content {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  margin-left: auto;
  margin-right: 0;
  max-width: 70%;
}

.timeline-item.user .message-header .message-meta .message-type {
  color: rgba(255, 255, 255, 0.9);
}

.timeline-item.user .message-header .message-meta .message-time {
  color: rgba(255, 255, 255, 0.7);
}

/* 时间轴线条 */
.timeline-line {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 2;
}

.timeline-dot {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  position: relative;
  z-index: 3;
  transition: all 0.3s ease;
}

.timeline-dot:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.timeline-dot.streaming {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  animation: pulse 2s infinite;
}

.message-icon {
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.timeline-connector {
  width: 2px;
  flex: 1;
  background: linear-gradient(180deg, #e2e8f0 0%, #cbd5e1 100%);
  margin-top: 8px;
  position: relative;
}

.timeline-connector::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, transparent 0%, #3b82f6 50%, transparent 100%);
  opacity: 0.3;
}

/* 消息内容 */
.timeline-content {
  flex: 1;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  position: relative;
  max-width: 75%;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: 20px;
  left: -8px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 8px 8px 8px 0;
  border-color: transparent white transparent transparent;
}

.timeline-item.user .timeline-content::before {
  left: auto;
  right: -8px;
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent #3b82f6;
}

/* 消息头部 */
.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-type {
  font-size: 14px;
  font-weight: 600;
  color: #475569;
}

.message-time {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
}

/* 状态指示器 */
.status-indicator {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 8px;
  font-weight: 500;
}

.status-indicator.sending {
  background: #fef3c7;
  color: #92400e;
}

.status-indicator.streaming {
  background: #dcfce7;
  color: #166534;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator.error {
  background: #fee2e2;
  color: #dc2626;
}

/* 打字动画 */
.typing-dots {
  display: flex;
  gap: 2px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #166534;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 消息体 */
.message-body {
  line-height: 1.6;
  color: #1e293b;
}

.message-text {
  font-size: 15px;
}

.message-text p {
  margin: 0 0 12px 0;
}

.message-text p:last-child {
  margin-bottom: 0;
}

/* SQL代码块 */
.sql-block {
  background: #1e293b;
  border-radius: 12px;
  overflow: hidden;
  margin: 12px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sql-header {
  background: #334155;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #475569;
}

.sql-label {
  font-size: 12px;
  font-weight: 600;
  color: #e2e8f0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.copy-btn {
  background: rgba(59, 130, 246, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 6px;
  padding: 4px 8px;
  color: #93c5fd;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: rgba(59, 130, 246, 0.3);
  color: #dbeafe;
}

.sql-content {
  margin: 0 !important;
  background: transparent !important;
}

/* 代码块样式 */
.code-block {
  border-radius: 8px;
  margin: 8px 0;
  font-size: 14px;
}

/* 消息标签 */
.message-tags {
  display: flex;
  gap: 6px;
  margin-top: 12px;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.region-analysis {
  background: #dbeafe;
  color: #1d4ed8;
}

.region-sql {
  background: #f3e8ff;
  color: #7c3aed;
}

.region-explanation {
  background: #dcfce7;
  color: #166534;
}

.region-data {
  background: #fef3c7;
  color: #92400e;
}

.region-visualization {
  background: #fee2e2;
  color: #dc2626;
}

/* 流式指示器 */
.streaming-indicator .timeline-dot {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  position: relative;
}

.pulse-ring {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #10b981;
  border-radius: 50%;
  animation: pulse-ring 2s infinite;
  opacity: 0.6;
}

.streaming-text {
  font-style: italic;
  color: #059669;
  font-weight: 500;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

/* 可折叠区域样式 - 优化性能 */
.collapsible-region {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
  will-change: box-shadow;
  contain: layout style;
}

.collapsible-region:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.region-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.region-header:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.region-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #334155;
}

.region-icon {
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.region-name {
  font-size: 16px;
}

.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #10b981;
  font-weight: 500;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.region-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-count {
  font-size: 12px;
  color: #64748b;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 6px;
}

.collapse-btn {
  background: none;
  border: none;
  font-size: 16px;
  color: #64748b;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.collapse-btn.collapsed {
  transform: rotate(-90deg);
}

.collapse-btn.expanded {
  transform: rotate(0deg);
}

/* 区域内容 - 优化动画性能 */
.region-content {
  padding: 0;
  will-change: transform, opacity;
  contain: layout style;
}

.collapsible-region.expanded .region-content {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.region-messages {
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
}

.region-message {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 3px solid #3b82f6;
}

.region-message:last-child {
  margin-bottom: 0;
}

.region-message.user {
  border-left-color: #10b981;
  background: #f0fdf4;
}

.region-message .message-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  font-size: 12px;
}

.region-message .message-source {
  font-weight: 600;
  color: #475569;
}

.region-message .message-time {
  color: #64748b;
}

.region-message .message-status {
  margin-left: auto;
}

.region-message .message-content {
  color: #334155;
  line-height: 1.6;
}

/* 区域摘要 */
.region-summary {
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #f1f5f9;
}

.summary-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: #475569;
}

.summary-content {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* 消息历史详情 */
.region-messages-details {
  margin-top: 12px;
  border-top: 1px solid #f1f5f9;
}

.messages-summary {
  padding: 12px 20px;
  background: #f8fafc;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  border: none;
  outline: none;
  user-select: none;
  transition: background-color 0.2s ease;
}

.messages-summary:hover {
  background: #f1f5f9;
}

.messages-summary::marker {
  content: '▶ ';
  color: #64748b;
}

.region-messages-details[open] .messages-summary::marker {
  content: '▼ ';
}

/* 折叠状态优化 */
.collapsible-region.collapsed .region-content {
  display: none;
}

.collapsible-region.expanded .region-content {
  display: block;
  animation: slideDown 0.3s ease-out;
}

/* 复制按钮优化 */
.copy-btn {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #64748b;
}

.copy-btn:hover {
  background: #f1f5f9;
  color: #334155;
  transform: scale(1.1);
}

.copy-btn:active {
  transform: scale(0.95);
}

/* Markdown样式优化 */
.markdown-paragraph {
  margin: 0.5rem 0;
  line-height: 1.6;
  color: #374151;
}

.markdown-h1, .markdown-h2, .markdown-h3, .markdown-h4, .markdown-h5, .markdown-h6 {
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
  line-height: 1.3;
  color: #1f2937;
}

.markdown-h1 { font-size: 1.5rem; }
.markdown-h2 { font-size: 1.25rem; }
.markdown-h3 { font-size: 1.125rem; }
.markdown-h4 { font-size: 1rem; }
.markdown-h5 { font-size: 0.875rem; }
.markdown-h6 { font-size: 0.75rem; }

.markdown-ul, .markdown-ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.markdown-li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.markdown-link {
  color: #3b82f6;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.markdown-link:hover {
  color: #2563eb;
}

.markdown-strong {
  font-weight: 600;
  color: #1f2937;
}

.markdown-em {
  font-style: italic;
  color: #4b5563;
}

.markdown-blockquote {
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  border-left: 4px solid #e5e7eb;
  background-color: #f9fafb;
  color: #6b7280;
  font-style: italic;
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.markdown-th, .markdown-td {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.markdown-th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.markdown-td {
  color: #4b5563;
}

.markdown-hr {
  margin: 1.5rem 0;
  border: none;
  border-top: 1px solid #e5e7eb;
}

.inline-code {
  background-color: #f1f5f9;
  color: #e11d48;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

/* 区域特定样式 */
.analysis-region .region-header {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.sql-region .region-header {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.explanation-region .region-header {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
}

.data-region .region-header {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

.visualization-region .region-header {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
}

.process-region .region-header {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .timeline-container {
    padding: 16px;
  }

  .timeline-item {
    gap: 12px;
    margin-bottom: 24px;
  }

  .timeline-dot {
    width: 40px;
    height: 40px;
  }

  .message-icon {
    font-size: 16px;
  }

  .timeline-content {
    padding: 16px;
    max-width: 85%;
  }

  .timeline-item.user .timeline-content {
    max-width: 85%;
  }

  .region-header {
    padding: 12px 16px;
  }

  .region-title {
    gap: 8px;
  }

  .region-icon {
    font-size: 18px;
  }

  .region-name {
    font-size: 14px;
  }

  .region-messages,
  .region-summary {
    padding: 12px 16px;
  }
}
