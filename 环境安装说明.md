# ChatDB 环境安装说明

## 🎯 概述

为了解决 ChatDB 项目的环境依赖问题，我们提供了多种一键安装 Python 3.9+ 和 Node.js 16+ 的解决方案。

## 📦 安装脚本列表

### 🚀 主要安装脚本

| 脚本名称 | 功能描述 | 适用场景 | 需要权限 |
|---------|---------|---------|---------|
| **环境安装向导.bat** | 智能检测环境并推荐最佳安装方式 | 首次使用推荐 | 部分需要 |
| **install-environment.bat** | 自动下载官方安装包并静默安装 | 网络良好时使用 | 管理员 |
| **install-with-chocolatey.bat** | 使用 Chocolatey 包管理器安装 | 已有 Chocolatey 时使用 | 管理员 |
| **manual-install-guide.bat** | 提供详细的手动安装指南 | 网络问题或特殊需求 | 无 |

### 🛠️ 辅助脚本

| 脚本名称 | 功能描述 |
|---------|---------|
| **START.bat** | 完整功能的 ChatDB 启动脚本 |
| **STOP.bat** | 完整功能的 ChatDB 停止脚本 |
| **simple-start.bat** | 简化版启动脚本 |
| **simple-stop.bat** | 简化版停止脚本 |
| **simple-status.bat** | 服务状态检查脚本 |

## 🔧 使用方法

### 方法一：智能向导（推荐）

```batch
# 双击运行
环境安装向导.bat
```

**特点：**
- 🔍 自动检测当前环境
- 💡 智能推荐最佳安装方式
- 🎯 根据系统状态选择合适的安装脚本
- ✅ 环境满足要求时可直接启动 ChatDB

### 方法二：自动下载安装

```batch
# 以管理员身份运行
install-environment.bat
```

**特点：**
- 📥 自动下载 Python 3.11.9 和 Node.js 20.18.0 LTS
- 🔧 静默安装，自动配置环境变量
- ✅ 安装完成后自动验证
- 🧹 自动清理下载文件

### 方法三：Chocolatey 安装

```batch
# 以管理员身份运行
install-with-chocolatey.bat
```

**特点：**
- 📦 使用 Chocolatey 包管理器
- 🔄 自动安装 Chocolatey（如果未安装）
- 🎯 安装最新稳定版本
- 🛠️ 便于后续管理和更新

### 方法四：手动安装指南

```batch
# 直接运行
manual-install-guide.bat
```

**特点：**
- 📖 详细的安装步骤说明
- 🌐 提供官方和国内镜像下载链接
- 🔧 故障排除指南
- 🔗 快速访问下载页面

## 📋 安装版本说明

### Python 版本
- **推荐版本**: Python 3.11.9
- **最低要求**: Python 3.9+
- **安装位置**: `C:\Python311\` (默认)
- **包含组件**: pip, tcl/tk, 标准库

### Node.js 版本
- **推荐版本**: Node.js 20.18.0 LTS
- **最低要求**: Node.js 16+
- **安装位置**: `C:\Program Files\nodejs\` (默认)
- **包含组件**: npm, npx

## 🔍 环境检测

所有脚本都包含智能环境检测功能：

### 检测项目
- ✅ Python 版本检查
- ✅ Node.js 版本检查
- ✅ pip 可用性检查
- ✅ npm 可用性检查
- ✅ 网络连接检查
- ✅ Chocolatey 安装状态
- ✅ 管理员权限检查

### 检测结果
- 🟢 **满足要求**: 直接启动 ChatDB
- 🟡 **部分满足**: 升级现有软件
- 🔴 **不满足**: 全新安装

## 🛠️ 故障排除

### 常见问题

#### 1. "不是内部或外部命令"错误
**原因**: 软件未添加到 PATH 环境变量
**解决**: 
- 重新安装时确保勾选 "Add to PATH"
- 重启命令行窗口
- 重启计算机

#### 2. 网络下载失败
**原因**: 网络连接问题或防火墙阻止
**解决**:
- 使用手动安装指南
- 临时关闭防火墙
- 使用手机热点网络

#### 3. 权限不足
**原因**: 需要管理员权限安装软件
**解决**:
- 右键选择"以管理员身份运行"
- 临时关闭 UAC

#### 4. Chocolatey 安装失败
**原因**: PowerShell 执行策略限制
**解决**:
- 以管理员身份运行 PowerShell
- 执行: `Set-ExecutionPolicy Bypass -Scope Process`

## 📁 文件结构

```
chatdb/
├── 环境安装向导.bat           # 主安装向导
├── install-environment.bat    # 自动下载安装
├── install-with-chocolatey.bat # Chocolatey安装
├── manual-install-guide.bat   # 手动安装指南
├── START.bat                  # 主启动脚本
├── STOP.bat                   # 主停止脚本
├── simple-start.bat           # 简化启动脚本
├── simple-stop.bat            # 简化停止脚本
├── simple-status.bat          # 状态检查脚本
├── 批处理文件问题修复指南.md   # 问题修复指南
└── 环境安装说明.md            # 本文档
```

## 🚀 快速开始

### 新用户推荐流程

1. **运行环境检测**
   ```batch
   环境安装向导.bat
   ```

2. **根据推荐选择安装方式**
   - 网络良好 + 有管理员权限 → 自动下载安装
   - 已有 Chocolatey → Chocolatey 安装
   - 网络问题 → 手动安装指南

3. **验证安装**
   ```batch
   python --version
   node --version
   ```

4. **启动 ChatDB**
   ```batch
   START.bat
   ```

### 已有环境用户

1. **检查环境**
   ```batch
   环境安装向导.bat
   ```

2. **直接启动**（如果环境满足要求）
   ```batch
   START.bat
   ```

## 💡 最佳实践

1. **首次安装**: 使用 `环境安装向导.bat`
2. **网络良好**: 优先选择自动下载安装
3. **企业环境**: 考虑使用 Chocolatey
4. **网络受限**: 使用手动安装指南
5. **安装后**: 重启命令行窗口刷新环境变量

## 📞 技术支持

如果遇到问题：

1. 查看 `批处理文件问题修复指南.md`
2. 运行 `simple-status.bat` 检查状态
3. 查看日志文件 `logs\` 目录
4. 尝试不同的安装方式

---

*最后更新: 2024年12月*
