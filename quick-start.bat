@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 快速启动脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0A

:: 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo [INFO] 项目根目录: %PROJECT_ROOT%
echo.

:: 检查必要的软件
echo [INFO] 检查必要软件...

:: 检查Python
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Python 已安装
    python --version
) else (
    echo [✗] Python 未安装，请先安装 Python 3.9+
    echo     下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查Node.js
node --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Node.js 已安装
    node --version
) else (
    echo [✗] Node.js 未安装，请先安装 Node.js
    echo     下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo.

:: 创建日志目录
if not exist "logs" mkdir logs

:: 1. 安装后端依赖
echo [1/4] 检查后端环境...
cd /d "%PROJECT_ROOT%\backend"

:: 检查虚拟环境
if not exist "venv" (
    echo [INFO] 创建Python虚拟环境...
    python -m venv venv
    if %errorLevel% neq 0 (
        echo [✗] 虚拟环境创建失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo [INFO] 激活虚拟环境...
call venv\Scripts\activate.bat

:: 安装依赖
echo [INFO] 检查Python依赖...
pip list | findstr fastapi >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] 安装Python依赖...
    pip install -r requirements.txt
    if %errorLevel% neq 0 (
        echo [✗] Python依赖安装失败
        pause
        exit /b 1
    )
)

:: 2. 安装前端依赖
echo.
echo [2/4] 检查前端环境...
cd /d "%PROJECT_ROOT%\frontend"

if not exist "node_modules" (
    echo [INFO] 安装前端依赖...
    npm install
    if %errorLevel% neq 0 (
        echo [✗] 前端依赖安装失败
        pause
        exit /b 1
    )
)

:: 3. 检查配置文件
echo.
echo [3/4] 检查配置文件...
cd /d "%PROJECT_ROOT%"

if not exist "backend\.env" (
    echo [!] 环境配置文件不存在
    if exist "backend\.env.template" (
        echo [INFO] 复制模板文件...
        copy "backend\.env.template" "backend\.env"
    ) else (
        echo [INFO] 创建基本配置文件...
        echo # ChatDB 基本配置 > "backend\.env"
        echo OPENAI_API_KEY=your_openai_api_key_here >> "backend\.env"
        echo MYSQL_SERVER=localhost >> "backend\.env"
        echo MYSQL_PORT=3306 >> "backend\.env"
        echo MYSQL_USER=root >> "backend\.env"
        echo MYSQL_PASSWORD=mysql >> "backend\.env"
        echo MYSQL_DB=chatdb >> "backend\.env"
        echo NEO4J_URI=bolt://localhost:7687 >> "backend\.env"
        echo NEO4J_USER=neo4j >> "backend\.env"
        echo NEO4J_PASSWORD=password >> "backend\.env"
        echo MILVUS_HOST=localhost >> "backend\.env"
        echo MILVUS_PORT=19530 >> "backend\.env"
    )
    echo [!] 请编辑 backend\.env 文件配置数据库连接信息
    echo [INFO] 是否现在编辑配置文件? (Y/N)
    set /p edit_choice=
    if /i "%edit_choice%"=="Y" (
        notepad backend\.env
    )
)

:: 4. 启动服务
echo.
echo [4/4] 启动服务...

:: 启动后端服务（后台运行）
echo [INFO] 启动FastAPI后端服务...
cd /d "%PROJECT_ROOT%\backend"
start "ChatDB Backend" cmd /c "call venv\Scripts\activate.bat && python main.py > ..\logs\backend.log 2>&1"

:: 等待后端启动
echo [INFO] 等待后端服务启动...
timeout /t 8 /nobreak >nul

:: 检查后端是否启动成功
netstat -an | findstr :8000 >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 后端服务启动成功 (http://localhost:8000)
) else (
    echo [!] 后端服务可能启动失败，请检查日志: logs\backend.log
)

:: 启动前端服务（后台运行）
echo.
echo [INFO] 启动React前端服务...
cd /d "%PROJECT_ROOT%\frontend"
start "ChatDB Frontend" cmd /c "npm start > ..\logs\frontend.log 2>&1"

:: 等待前端启动
echo [INFO] 等待前端服务启动...
timeout /t 15 /nobreak >nul

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 服务地址:
echo   前端应用: http://localhost:3000
echo   后端API:  http://localhost:8000
echo   API文档:  http://localhost:8000/docs
echo.
echo 日志文件:
echo   后端日志: logs\backend.log
echo   前端日志: logs\frontend.log
echo.
echo 管理操作:
echo   查看状态: 运行 show-menu.bat
echo   停止服务: 关闭相应的命令行窗口
echo.
echo 按任意键打开前端应用...
pause >nul

:: 打开浏览器
start http://localhost:3000

echo.
echo [INFO] 应用已启动，浏览器将自动打开
echo [INFO] 如需停止服务，请关闭相应的命令行窗口
echo.
pause
