version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: chatdb-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: chatdb
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  neo4j:
    image: neo4j:4.4
    container_name: chatdb-neo4j
    restart: always
    environment:
      NEO4J_AUTH: neo4j/password
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs

  backend:
    build: ./backend
    container_name: chatdb-backend
    restart: always
    depends_on:
      - mysql
      - neo4j
    environment:
      - MYSQL_SERVER=mysql
      - MYSQL_USER=root
      - MYSQL_PASSWORD=password
      - MYSQL_DB=chatdb
      - MYSQL_PORT=3306
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=password
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app

  frontend:
    build: ./frontend
    container_name: chatdb-frontend
    restart: always
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules

volumes:
  mysql_data:
  neo4j_data:
  neo4j_logs:
