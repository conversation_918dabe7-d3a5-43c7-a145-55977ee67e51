/* Gemini风格样式 */

/* 聊天消息容器 */
.gemini-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.5) transparent;
}

.gemini-container::-webkit-scrollbar {
  width: 6px;
}

.gemini-container::-webkit-scrollbar-track {
  background: transparent;
}

.gemini-container::-webkit-scrollbar-thumb {
  background-color: rgba(203, 213, 225, 0.5);
  border-radius: 3px;
}

/* 分析内容容器特殊样式 */
.analysis-content-container {
  min-height: 300px;
  max-height: calc(70vh - 100px);
  display: flex;
  flex-direction: column;
  overflow-y: auto !important;
}

/* 分析内容格式化 */
.analysis-content {
  overflow-y: auto !important;
  padding: 0 !important;
  margin: 0 !important;
  display: block !important;
  min-height: 200px;
  max-height: none !important; /* 移除高度限制，允许内容自然扩展 */
  width: 100%;
}

.analysis-formatted-content {
  padding: 0;
  margin: 0;
  width: 100%;
}

/* 确保列表项正确显示 */
.analysis-content ul,
.analysis-content ol,
.sql-explanation-content ul,
.sql-explanation-content ol {
  padding-left: 1.5rem;
  margin: 0.75rem 0;
  display: block !important;
}

.analysis-content li,
.sql-explanation-content li {
  margin: 0.25rem 0;
  display: list-item !important;
}

/* 确保标题正确显示 */
.analysis-content h1,
.analysis-content h2,
.analysis-content h3,
.analysis-content h4,
.analysis-content h5,
.analysis-content h6,
.sql-explanation-content h1,
.sql-explanation-content h2,
.sql-explanation-content h3,
.sql-explanation-content h4,
.sql-explanation-content h5,
.sql-explanation-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  display: block !important;
}

/* 确保代码块正确显示 */
.analysis-content pre,
.sql-explanation-content pre {
  margin: 1rem 0;
  padding: 0.75rem;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  overflow-x: auto;
  display: block !important;
}

/* 确保段落间距正确 */
.analysis-content p,
.sql-explanation-content p {
  margin: 0.75rem 0;
  line-height: 1.6;
  display: block !important;
}

/* 消息块 */
.gemini-message {
  display: flex;
  gap: 16px;
  max-width: 100%;
  animation: fadeIn 0.3s ease;
}

/* 头像 */
.gemini-avatar {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f4ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
}

.gemini-avatar.system {
  background-color: #f0f4ff;
  color: #3b82f6;
}

.gemini-avatar.user {
  background-color: #f0fdf4;
  color: #10b981;
}

/* 消息内容 */
.gemini-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 4px;
}

/* 消息头部 */
.gemini-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.gemini-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.gemini-actions {
  display: flex;
  gap: 8px;
}

.gemini-action-button {
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.gemini-action-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

/* 消息体 */
.gemini-body {
  font-size: 15px;
  line-height: 1.6;
  color: #1f2937;
  overflow-wrap: break-word;
  word-break: break-word;
  overflow-y: visible;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* SQL代码块 */
.gemini-sql-block {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  margin: 8px 0;
}

.gemini-sql-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
}

.gemini-sql-title {
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
}

.gemini-sql-content {
  padding: 12px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  color: #334155;
  background-color: #f8fafc;
}

.gemini-sql-content pre {
  margin: 0;
  white-space: pre-wrap;
}

.gemini-sql-content code {
  font-family: inherit;
}

/* 加载动画 */
.gemini-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  color: #6b7280;
}

.gemini-loading-dots {
  display: flex;
  gap: 4px;
}

.gemini-loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #d1d5db;
  animation: pulse 1.5s infinite ease-in-out;
}

.gemini-loading-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.gemini-loading-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 暗色模式 */
.dark .gemini-container {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.dark .gemini-container::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .gemini-avatar {
  background-color: rgba(59, 130, 246, 0.2);
}

.dark .gemini-avatar.user {
  background-color: rgba(16, 185, 129, 0.2);
}

.dark .gemini-name {
  color: #e5e7eb;
}

.dark .gemini-body {
  color: #e5e7eb;
}

.dark .gemini-action-button {
  color: #9ca3af;
}

.dark .gemini-action-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
}

.dark .gemini-sql-block {
  background-color: #1e293b;
  border-color: #334155;
}

.dark .gemini-sql-header {
  background-color: #1e293b;
  border-color: #334155;
}

.dark .gemini-sql-title {
  color: #94a3b8;
}

.dark .gemini-sql-content {
  background-color: #0f172a;
  color: #e2e8f0;
}

.dark .gemini-loading-dot {
  background-color: #4b5563;
}

.dark .prose {
  color: #e5e7eb;
}

.dark .prose code {
  background-color: rgba(15, 23, 42, 0.5);
  color: #e2e8f0;
}

.dark .prose pre {
  background-color: #0f172a;
  color: #e2e8f0;
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose h5,
.dark .prose h6 {
  color: #f3f4f6;
  border-color: #374151;
}

.dark .prose strong {
  color: #f3f4f6;
}

.dark .prose ul,
.dark .prose ol {
  color: #e5e7eb;
}

.dark .prose blockquote {
  color: #d1d5db;
  border-color: #4b5563;
}

.dark .prose a {
  color: #60a5fa;
}

.dark .prose hr {
  border-color: #374151;
}

.dark .prose table {
  border-color: #374151;
}

.dark .prose thead {
  background-color: #1f2937;
  color: #f3f4f6;
}

.dark .prose tbody tr {
  border-color: #374151;
}

.dark .prose tbody td {
  border-color: #374151;
}

/* 流式显示光标样式 */
.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #3b82f6;
  margin-left: 2px;
  animation: blink 1s step-end infinite;
}

@keyframes blink {
  from, to { opacity: 1; }
  50% { opacity: 0; }
}

.dark .typing-cursor {
  background-color: #60a5fa;
}
