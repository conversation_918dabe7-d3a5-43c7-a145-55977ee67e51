@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title ChatDB 状态检查

echo ========================================
echo    ChatDB 状态检查 (Windows)
echo ========================================
echo.

echo [INFO] 检查时间: %date% %time%
echo.

:: 检查端口占用
echo [1/4] 检查端口占用...
echo.

:: 检查8000端口
netstat -ano | findstr :8000 >nul 2>&1
if errorlevel 1 (
    echo [X] 端口8000 (后端) - 未使用
) else (
    echo [√] 端口8000 (后端) - 正在使用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000 ^| findstr LISTENING') do (
        set "pid=%%a"
        for /f "tokens=1" %%b in ('tasklist /fi "pid eq !pid!" /fo table /nh 2^>nul') do (
            echo     进程: %%b (PID: !pid!)
        )
    )
)

:: 检查3000端口
netstat -ano | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo [X] 端口3000 (前端) - 未使用
) else (
    echo [√] 端口3000 (前端) - 正在使用
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 ^| findstr LISTENING') do (
        set "pid=%%a"
        for /f "tokens=1" %%b in ('tasklist /fi "pid eq !pid!" /fo table /nh 2^>nul') do (
            echo     进程: %%b (PID: !pid!)
        )
    )
)

:: 检查服务可用性
echo.
echo [2/4] 检查服务可用性...
echo.

:: 检查后端API
echo [INFO] 检查后端API...
powershell -Command "try { Invoke-RestMethod -Uri 'http://localhost:8000' -TimeoutSec 3 | Out-Null; Write-Host '[√] 后端API正常' } catch { Write-Host '[X] 后端API异常' }" 2>nul

:: 检查前端服务
echo [INFO] 检查前端服务...
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 3 | Out-Null; Write-Host '[√] 前端服务正常' } catch { Write-Host '[X] 前端服务异常' }" 2>nul

:: 检查环境
echo.
echo [3/4] 检查环境...
echo.

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo [X] Python - 未安装或未添加到PATH
) else (
    echo [√] Python - 已安装
    python --version
)

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [X] Node.js - 未安装或未添加到PATH
) else (
    echo [√] Node.js - 已安装
    node --version
)

:: 检查文件
echo.
echo [4/4] 检查文件...
echo.

if exist "backend\.env" (
    echo [√] 配置文件存在: backend\.env
) else (
    echo [X] 配置文件不存在: backend\.env
)

if exist "backend\venv" (
    echo [√] Python虚拟环境存在
) else (
    echo [X] Python虚拟环境不存在
)

if exist "frontend\node_modules" (
    echo [√] 前端依赖已安装
) else (
    echo [X] 前端依赖未安装
)

if exist "logs" (
    echo [√] 日志目录存在
    if exist "logs\backend.log" (
        echo     - 后端日志存在
    )
    if exist "logs\frontend.log" (
        echo     - 前端日志存在
    )
) else (
    echo [X] 日志目录不存在
)

echo.
echo ========================================
echo           检查完成
echo ========================================
echo.
echo 快速操作:
echo   启动服务: simple-start.bat
echo   停止服务: simple-stop.bat
echo   打开前端: http://localhost:3000
echo   查看API: http://localhost:8000/docs
echo.
pause
