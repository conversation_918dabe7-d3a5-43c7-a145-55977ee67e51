@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 环境安装脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0B

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 管理员权限检查通过
) else (
    echo [!] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置项目根目录
set PROJECT_ROOT=%~dp0
cd /d "%PROJECT_ROOT%"

echo [INFO] 项目根目录: %PROJECT_ROOT%
echo.

:: 创建必要的目录
echo [INFO] 创建必要目录...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
echo [✓] 目录创建完成
echo.

:: 检查和安装Python
echo [1/4] 检查Python环境...
python --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Python 已安装
    python --version
) else (
    echo [!] Python 未安装
    echo [INFO] 请手动安装Python 3.9+
    echo [INFO] 下载地址: https://www.python.org/downloads/
    echo [INFO] 安装时请勾选 "Add Python to PATH"
    start https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 检查pip
pip --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] pip 已安装
) else (
    echo [!] pip 未安装，请重新安装Python
    pause
    exit /b 1
)

:: 检查和安装Node.js
echo.
echo [2/4] 检查Node.js环境...
node --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Node.js 已安装
    node --version
    npm --version
) else (
    echo [!] Node.js 未安装
    echo [INFO] 请手动安装Node.js (推荐LTS版本)
    echo [INFO] 下载地址: https://nodejs.org/
    start https://nodejs.org/
    pause
    exit /b 1
)

:: 设置npm镜像源（可选）
echo [INFO] 设置npm镜像源为淘宝镜像...
npm config set registry https://registry.npmmirror.com/
echo [✓] npm镜像源设置完成

:: 安装后端依赖
echo.
echo [3/4] 安装后端依赖...
cd /d "%PROJECT_ROOT%\backend"

:: 创建虚拟环境
if not exist "venv" (
    echo [INFO] 创建Python虚拟环境...
    python -m venv venv
    if %errorLevel% neq 0 (
        echo [✗] 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo [✓] 虚拟环境创建成功
)

:: 激活虚拟环境并安装依赖
echo [INFO] 激活虚拟环境并安装依赖...
call venv\Scripts\activate.bat

:: 升级pip
python -m pip install --upgrade pip

:: 设置pip镜像源
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

:: 安装依赖
echo [INFO] 安装Python依赖包...
pip install -r requirements.txt
if %errorLevel% neq 0 (
    echo [✗] Python依赖安装失败
    echo [INFO] 尝试使用官方源重新安装...
    pip config unset global.index-url
    pip install -r requirements.txt
    if %errorLevel% neq 0 (
        echo [✗] Python依赖安装仍然失败，请检查网络连接
        pause
        exit /b 1
    )
)
echo [✓] Python依赖安装完成

:: 安装前端依赖
echo.
echo [4/4] 安装前端依赖...
cd /d "%PROJECT_ROOT%\frontend"

echo [INFO] 安装前端依赖包...
npm install
if %errorLevel% neq 0 (
    echo [✗] 前端依赖安装失败
    echo [INFO] 尝试清理缓存后重新安装...
    npm cache clean --force
    rmdir /s /q node_modules 2>nul
    npm install
    if %errorLevel% neq 0 (
        echo [✗] 前端依赖安装仍然失败，请检查网络连接
        pause
        exit /b 1
    )
)
echo [✓] 前端依赖安装完成

:: 环境配置检查
echo.
echo [INFO] 检查环境配置...
cd /d "%PROJECT_ROOT%"

if exist "backend\.env" (
    echo [✓] 后端环境配置文件存在
) else (
    echo [!] 后端环境配置文件不存在
    echo [INFO] 请确保 backend\.env 文件存在并配置正确
)

:: 创建快捷方式（可选）
echo.
echo [INFO] 创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\Desktop
if exist "%DESKTOP%" (
    echo [INFO] 在桌面创建启动快捷方式...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\启动ChatDB.lnk'); $Shortcut.TargetPath = '%PROJECT_ROOT%start-chatdb.bat'; $Shortcut.WorkingDirectory = '%PROJECT_ROOT%'; $Shortcut.IconLocation = 'shell32.dll,25'; $Shortcut.Save()"
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\停止ChatDB.lnk'); $Shortcut.TargetPath = '%PROJECT_ROOT%stop-chatdb.bat'; $Shortcut.WorkingDirectory = '%PROJECT_ROOT%'; $Shortcut.IconLocation = 'shell32.dll,28'; $Shortcut.Save()"
    
    echo [✓] 桌面快捷方式创建完成
)

echo.
echo ========================================
echo           环境安装完成！
echo ========================================
echo.
echo 安装摘要:
echo   [✓] Python环境和依赖
echo   [✓] Node.js环境和依赖  
echo   [✓] 项目目录结构
echo   [✓] 桌面快捷方式
echo.
echo 下一步:
echo   1. 确保MySQL、Neo4j、Milvus服务正在运行
echo   2. 检查 backend\.env 配置文件
echo   3. 运行 start-chatdb.bat 启动应用
echo.
echo 注意事项:
echo   - 首次运行会自动初始化数据库
echo   - 确保防火墙允许3000和8000端口
echo   - 如遇问题请查看logs目录下的日志文件
echo.
pause
