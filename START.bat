@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
title ChatDB 启动器

:: 设置颜色
color 0A

echo.
echo ╔════════════════════════════════════════╗
echo ║          ChatDB 启动器 v1.0            ║
echo ║        Windows 一键启动工具            ║
echo ╚════════════════════════════════════════╝
echo.

:: 获取脚本所在目录
set "ROOT_DIR=%~dp0"
cd /d "%ROOT_DIR%"

echo [INFO] 项目目录: %ROOT_DIR%
echo.

:: 步骤1: 环境检查
echo ┌─ 步骤 1/5: 环境检查 ─┐
echo.

:: 检查Python
echo [检查] Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未安装或未添加到PATH
    echo        请下载安装: https://www.python.org/downloads/
    echo        安装时请勾选 "Add Python to PATH"
    pause
    exit /b 1
)
for /f "tokens=2" %%v in ('python --version 2^>^&1') do set "PYTHON_VER=%%v"
echo [成功] Python %PYTHON_VER%

:: 检查Node.js
echo [检查] Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Node.js未安装或未添加到PATH
    echo        请下载安装: https://nodejs.org/
    pause
    exit /b 1
)
for /f %%v in ('node --version 2^>^&1') do set "NODE_VER=%%v"
echo [成功] Node.js %NODE_VER%

echo.
echo └─ 环境检查完成 ─┘
echo.

:: 步骤2: 后端准备
echo ┌─ 步骤 2/5: 后端环境准备 ─┐
echo.

cd /d "%ROOT_DIR%backend"

:: 创建虚拟环境
if not exist "venv" (
    echo [创建] Python虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [错误] 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境已创建
)

:: 激活虚拟环境
echo [激活] 虚拟环境...
call "venv\Scripts\activate.bat"
if %errorlevel% neq 0 (
    echo [错误] 虚拟环境激活失败
    pause
    exit /b 1
)

:: 检查并安装依赖
echo [检查] Python依赖...
pip show fastapi >nul 2>&1
if %errorlevel% neq 0 (
    echo [安装] Python依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo [错误] 依赖安装失败
        pause
        exit /b 1
    )
    echo [成功] Python依赖已安装
) else (
    echo [跳过] Python依赖已存在
)

echo.
echo └─ 后端环境准备完成 ─┘
echo.

:: 步骤3: 前端准备
echo ┌─ 步骤 3/5: 前端环境准备 ─┐
echo.

cd /d "%ROOT_DIR%frontend"

if not exist "node_modules" (
    echo [安装] 前端依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo [错误] 前端依赖安装失败
        pause
        exit /b 1
    )
    echo [成功] 前端依赖已安装
) else (
    echo [跳过] 前端依赖已存在
)

echo.
echo └─ 前端环境准备完成 ─┘
echo.

:: 步骤4: 配置检查
echo ┌─ 步骤 4/5: 配置文件检查 ─┐
echo.

cd /d "%ROOT_DIR%"

if not exist "backend\.env" (
    echo [创建] 默认配置文件...
    (
        echo # ChatDB 配置文件 - 请根据实际情况修改
        echo OPENAI_API_KEY=your_openai_api_key_here
        echo OPENAI_API_BASE=https://api.deepseek.com/v1
        echo LLM_MODEL=deepseek-chat
        echo.
        echo # MySQL 数据库配置
        echo MYSQL_SERVER=localhost
        echo MYSQL_PORT=3306
        echo MYSQL_USER=root
        echo MYSQL_PASSWORD=mysql
        echo MYSQL_DB=chatdb
        echo.
        echo # Neo4j 图数据库配置
        echo NEO4J_URI=bolt://localhost:7687
        echo NEO4J_USER=neo4j
        echo NEO4J_PASSWORD=password
        echo.
        echo # Milvus 向量数据库配置
        echo MILVUS_HOST=localhost
        echo MILVUS_PORT=19530
    ) > "backend\.env"
    echo [警告] 已创建默认配置文件: backend\.env
    echo        请根据实际情况修改数据库连接信息
) else (
    echo [成功] 配置文件已存在
)

:: 创建日志目录
if not exist "logs" (
    mkdir "logs"
    echo [创建] 日志目录
)

echo.
echo └─ 配置检查完成 ─┘
echo.

:: 步骤5: 启动服务
echo ┌─ 步骤 5/5: 启动服务 ─┐
echo.

:: 启动后端
echo [启动] 后端服务...
cd /d "%ROOT_DIR%backend"
start "ChatDB-Backend" /min cmd /c "call venv\Scripts\activate.bat && python main.py > ..\logs\backend.log 2>&1"

:: 等待后端启动
echo [等待] 后端服务启动中...
timeout /t 8 /nobreak >nul

:: 启动前端
echo [启动] 前端服务...
cd /d "%ROOT_DIR%frontend"
start "ChatDB-Frontend" /min cmd /c "npm start > ..\logs\frontend.log 2>&1"

:: 等待前端启动
echo [等待] 前端服务启动中...
timeout /t 15 /nobreak >nul

echo.
echo └─ 服务启动完成 ─┘
echo.

:: 显示结果
echo ╔════════════════════════════════════════╗
echo ║              启动完成！                ║
echo ╚════════════════════════════════════════╝
echo.
echo 🌐 访问地址:
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:8000
echo    API文档:  http://localhost:8000/docs
echo.
echo 📁 日志文件:
echo    后端日志: logs\backend.log
echo    前端日志: logs\frontend.log
echo.
echo 🛠️  管理工具:
echo    停止服务: simple-stop.bat
echo    查看状态: simple-status.bat
echo.

:: 询问是否打开浏览器
set /p "open_browser=是否打开浏览器访问应用? (Y/N): "
if /i "%open_browser%"=="Y" (
    echo [打开] 浏览器...
    start http://localhost:3000
)

echo.
echo 服务已在后台运行，可以关闭此窗口。
echo 如需停止服务，请运行 simple-stop.bat
echo.
pause
