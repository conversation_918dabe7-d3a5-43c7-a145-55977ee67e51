@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
title ChatDB 环境安装向导

color 0F

echo.
echo ╔════════════════════════════════════════╗
echo ║        ChatDB 环境安装向导 v1.0        ║
echo ║      自动检测并选择最佳安装方式        ║
echo ╚════════════════════════════════════════╝
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  [警告] 未以管理员身份运行
    echo    某些安装方式可能需要管理员权限
    echo.
    set /p "continue=是否继续? (Y/N): "
    if /i not "!continue!"=="Y" (
        echo [退出] 请右键选择"以管理员身份运行"
        pause
        exit /b 1
    )
    echo.
)

:: 环境检测
echo ┌─ 环境检测 ─┐
echo.

set "PYTHON_OK=0"
set "NODEJS_OK=0"
set "NETWORK_OK=0"
set "CHOCO_OK=0"

:: 检查Python
echo [检测] Python 环境...
python --version >nul 2>&1
if %errorlevel% == 0 (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        set "PYTHON_VER=%%v"
        echo [发现] Python !PYTHON_VER!
        
        :: 检查版本是否满足要求
        echo !PYTHON_VER! | findstr /r "^3\.[9-9]\|^3\.1[0-9]\|^[4-9]\." >nul
        if !errorlevel! == 0 (
            echo [✅] Python 版本满足要求
            set "PYTHON_OK=1"
        ) else (
            echo [⚠️] Python 版本过低，需要 3.9+
        )
    )
) else (
    echo [❌] Python 未安装
)

:: 检查Node.js
echo [检测] Node.js 环境...
node --version >nul 2>&1
if %errorlevel% == 0 (
    for /f %%v in ('node --version 2^>^&1') do (
        set "NODE_VER=%%v"
        echo [发现] Node.js !NODE_VER!
        
        :: 检查版本是否满足要求
        echo !NODE_VER! | findstr /r "^v1[6-9]\.\|^v[2-9][0-9]\." >nul
        if !errorlevel! == 0 (
            echo [✅] Node.js 版本满足要求
            set "NODEJS_OK=1"
        ) else (
            echo [⚠️] Node.js 版本过低，需要 16+
        )
    )
) else (
    echo [❌] Node.js 未安装
)

:: 检查网络连接
echo [检测] 网络连接...
ping -n 1 www.python.org >nul 2>&1
if %errorlevel% == 0 (
    echo [✅] 网络连接正常
    set "NETWORK_OK=1"
) else (
    echo [❌] 网络连接异常
)

:: 检查Chocolatey
echo [检测] Chocolatey 包管理器...
choco --version >nul 2>&1
if %errorlevel% == 0 (
    echo [✅] Chocolatey 已安装
    set "CHOCO_OK=1"
) else (
    echo [❌] Chocolatey 未安装
)

echo.
echo └─ 环境检测完成 ─┘
echo.

:: 如果环境已满足要求
if !PYTHON_OK! == 1 if !NODEJS_OK! == 1 (
    echo ╔════════════════════════════════════════╗
    echo ║            🎉 环境已就绪！             ║
    echo ╚════════════════════════════════════════╝
    echo.
    echo [✅] Python !PYTHON_VER! - 满足要求
    echo [✅] Node.js !NODE_VER! - 满足要求
    echo.
    echo [🚀] 可以直接启动 ChatDB 了！
    echo.
    
    set /p "start_now=是否现在启动 ChatDB? (Y/N): "
    if /i "!start_now!"=="Y" (
        echo [启动] 正在启动 ChatDB...
        if exist "START.bat" (
            call START.bat
        ) else (
            call simple-start.bat
        )
        exit /b 0
    ) else (
        echo [提示] 随时可以运行 START.bat 启动 ChatDB
        pause
        exit /b 0
    )
)

:: 显示安装选项
echo ╔════════════════════════════════════════╗
echo ║              安装选项                  ║
echo ╚════════════════════════════════════════╝
echo.

echo 根据您的系统环境，推荐以下安装方式:
echo.

:: 推荐安装方式
if !NETWORK_OK! == 1 (
    if !CHOCO_OK! == 1 (
        echo [推荐] 选项 2: 使用 Chocolatey 安装 (最简单)
    ) else (
        echo [推荐] 选项 1: 自动下载安装 (最完整)
    )
) else (
    echo [推荐] 选项 3: 手动安装指南 (网络问题时使用)
)

echo.
echo ┌─ 可用安装方式 ─┐
echo.
echo [1] 🔄 自动下载安装
echo     - 自动下载官方安装包
echo     - 静默安装，自动配置
echo     - 需要: 网络连接 + 管理员权限
if !NETWORK_OK! == 0 echo     - ❌ 当前网络不可用
echo.

echo [2] 📦 使用 Chocolatey 安装
echo     - 使用包管理器安装
echo     - 自动管理依赖和更新
echo     - 需要: 网络连接 + 管理员权限
if !CHOCO_OK! == 1 (
    echo     - ✅ Chocolatey 已安装
) else (
    echo     - ⚠️  需要先安装 Chocolatey
)
if !NETWORK_OK! == 0 echo     - ❌ 当前网络不可用
echo.

echo [3] 📖 手动安装指南
echo     - 提供详细安装步骤
echo     - 适合网络问题或特殊需求
echo     - 需要: 手动下载和安装
echo     - ✅ 始终可用
echo.

echo [4] 🔍 重新检测环境
echo     - 重新扫描系统环境
echo     - 适合安装后验证
echo.

echo [5] 🚪 退出向导
echo.
echo └─ 安装方式选择 ─┘
echo.

set /p "choice=请选择安装方式 (1-5): "

if "%choice%"=="1" (
    if !NETWORK_OK! == 0 (
        echo [错误] 网络连接不可用，无法自动下载
        echo [建议] 请选择选项 3 (手动安装指南)
        pause
        goto :eof
    )
    echo [启动] 自动下载安装...
    call install-environment.bat
)

if "%choice%"=="2" (
    if !NETWORK_OK! == 0 (
        echo [错误] 网络连接不可用，无法使用 Chocolatey
        echo [建议] 请选择选项 3 (手动安装指南)
        pause
        goto :eof
    )
    echo [启动] Chocolatey 安装...
    call install-with-chocolatey.bat
)

if "%choice%"=="3" (
    echo [启动] 手动安装指南...
    call manual-install-guide.bat
)

if "%choice%"=="4" (
    echo [重新检测] 环境状态...
    timeout /t 2 /nobreak >nul
    call "%~f0"
    exit /b 0
)

if "%choice%"=="5" (
    echo [退出] 感谢使用 ChatDB 环境安装向导！
    echo.
    echo 💡 提示:
    echo    - 环境安装完成后，运行 START.bat 启动 ChatDB
    echo    - 如有问题，可重新运行此向导
    echo.
    pause
    exit /b 0
)

:: 无效选择
echo [错误] 无效的选择，请输入 1-5
timeout /t 2 /nobreak >nul
call "%~f0"
