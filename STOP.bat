@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
title ChatDB 停止器

:: 设置颜色
color 0C

echo.
echo ╔════════════════════════════════════════╗
echo ║          ChatDB 停止器 v1.0            ║
echo ║        Windows 服务停止工具            ║
echo ╚════════════════════════════════════════╝
echo.

echo [INFO] 正在停止 ChatDB 相关服务...
echo.

:: 停止命名窗口
echo ┌─ 停止命名窗口 ─┐
echo.

tasklist /fi "WindowTitle eq ChatDB-Backend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul
if %errorlevel% == 0 (
    echo [停止] ChatDB-Backend 窗口...
    taskkill /fi "WindowTitle eq ChatDB-Backend*" /f >nul 2>&1
    if %errorlevel% == 0 (
        echo [成功] 后端窗口已停止
    ) else (
        echo [失败] 后端窗口停止失败
    )
) else (
    echo [跳过] 未找到后端窗口
)

tasklist /fi "WindowTitle eq ChatDB-Frontend*" /fo table /nh 2>nul | findstr /v "INFO:" >nul
if %errorlevel% == 0 (
    echo [停止] ChatDB-Frontend 窗口...
    taskkill /fi "WindowTitle eq ChatDB-Frontend*" /f >nul 2>&1
    if %errorlevel% == 0 (
        echo [成功] 前端窗口已停止
    ) else (
        echo [失败] 前端窗口停止失败
    )
) else (
    echo [跳过] 未找到前端窗口
)

echo.
echo └─ 命名窗口处理完成 ─┘
echo.

:: 停止端口占用进程
echo ┌─ 停止端口占用进程 ─┐
echo.

:: 停止8000端口进程
echo [检查] 端口8000 (后端)...
set "found_8000=0"
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :8000') do (
    set "pid=%%a"
    if "!pid!" neq "" if "!pid!" neq "0" (
        echo [停止] 进程 !pid! (端口8000)
        taskkill /pid !pid! /f >nul 2>&1
        set "found_8000=1"
    )
)
if !found_8000! == 0 (
    echo [跳过] 端口8000未被占用
)

:: 停止3000端口进程
echo [检查] 端口3000 (前端)...
set "found_3000=0"
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :3000') do (
    set "pid=%%a"
    if "!pid!" neq "" if "!pid!" neq "0" (
        echo [停止] 进程 !pid! (端口3000)
        taskkill /pid !pid! /f >nul 2>&1
        set "found_3000=1"
    )
)
if !found_3000! == 0 (
    echo [跳过] 端口3000未被占用
)

echo.
echo └─ 端口进程处理完成 ─┘
echo.

:: 等待进程完全停止
echo [等待] 进程完全停止...
timeout /t 3 /nobreak >nul

:: 验证停止结果
echo ┌─ 验证停止结果 ─┐
echo.

:: 检查端口8000
netstat -ano 2>nul | findstr :8000 >nul
if %errorlevel% == 0 (
    echo [警告] 端口8000仍被占用
    for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :8000') do (
        echo        进程ID: %%a
    )
) else (
    echo [成功] 端口8000已释放
)

:: 检查端口3000
netstat -ano 2>nul | findstr :3000 >nul
if %errorlevel% == 0 (
    echo [警告] 端口3000仍被占用
    for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr :3000') do (
        echo        进程ID: %%a
    )
) else (
    echo [成功] 端口3000已释放
)

echo.
echo └─ 验证完成 ─┘
echo.

:: 显示结果
echo ╔════════════════════════════════════════╗
echo ║              停止完成！                ║
echo ╚════════════════════════════════════════╝
echo.
echo 📊 服务状态:
echo    后端服务: 已停止
echo    前端服务: 已停止
echo.
echo 🛠️  其他操作:
echo    重新启动: START.bat
echo    查看状态: simple-status.bat
echo    查看日志: logs\ 目录
echo.

echo 所有服务已停止，按任意键退出...
pause >nul
