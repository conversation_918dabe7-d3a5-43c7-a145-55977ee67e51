/* 新的图表样式文件 - 整合和优化所有样式 */

/* 表节点样式 */
.table-node {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  min-width: 240px;
  max-width: 300px;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  transition: box-shadow 0.2s ease, transform 0.1s ease;
  z-index: 10;
  position: relative;
  cursor: move !important;
  pointer-events: all !important;
  user-select: none !important;
  touch-action: none !important;
}

.table-node:hover, .table-node.hovered {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  z-index: 20;
}

.table-node.selected {
  box-shadow: 0 0 0 2px #3b82f6;
}

/* 节点头部样式 */
.node-header {
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 10px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab !important;
  user-select: none !important;
  touch-action: none !important;
  pointer-events: all !important;
}

.node-header .node-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #334155;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-header .node-icon {
  color: #3b82f6;
  font-size: 16px;
}

.node-header .node-description {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 节点主体样式 */
.node-body {
  padding: 8px 0;
  max-height: 400px;
  overflow-y: auto;
}

.node-section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  background-color: #f8fafc;
  border-bottom: 1px solid #f1f5f9;
}

.column-count {
  background-color: #e2e8f0;
  color: #475569;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

/* 列样式 */
.columns-container {
  padding: 0;
}

.column {
  padding: 6px 12px;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.column:hover, .column.hovered {
  background-color: #f8fafc;
}

.column.primary-key {
  background-color: #f0f9ff;
}

.column.foreign-key {
  background-color: #fffbeb;
}

.column.primary-key.foreign-key {
  background: linear-gradient(to right, #f0f9ff 50%, #fffbeb 50%);
}

.column-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.column-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #334155;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.column-icon-container {
  display: flex;
  gap: 4px;
}

.column-icon {
  font-size: 12px;
}

.primary-key-icon {
  color: #2563eb;
}

.foreign-key-icon {
  color: #d97706;
}

.column-name-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.column-type {
  font-size: 11px;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

/* 连接点样式 */
.column-handle {
  width: 10px !important;
  height: 10px !important;
  background-color: #3b82f6;
  border: 2px solid white;
  box-shadow: 0 0 0 1px #3b82f6;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 100;
}

.column:hover .column-handle, 
.column.hovered .column-handle, 
.column-handle.visible {
  opacity: 1;
}

.source-handle {
  right: -8px;
}

.target-handle {
  left: -8px;
}

/* 拖拽目标样式 */
.potential-target {
  background-color: #f1f5f9;
}

.active-target {
  background-color: #e0f2fe;
  border: 1px dashed #3b82f6;
}

/* 关系边样式 */
.react-flow__edge-path {
  stroke-width: 2.5;
  transition: stroke-width 0.2s ease;
}

.react-flow__edge-path.highlighted {
  stroke-width: 4;
}

.edge-label-container {
  pointer-events: all;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.edge-label-container:hover, 
.edge-label-container.highlighted {
  transform: scale(1.1) !important;
}

/* 关系类型特定样式 */
.relationship-1-to-1 {
  stroke: #8b5cf6;
}

.relationship-1-to-N {
  stroke: #0ea5e9;
}

.relationship-N-to-M {
  stroke: #f59e0b;
}

/* 图表容器样式 */
.diagram-container {
  width: 100%;
  height: 600px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.diagram-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f8fafc;
  border-radius: 8px 8px 0 0;
  border: 1px solid #e2e8f0;
  border-bottom: none;
}

/* 确保ReactFlow组件正常工作的关键样式 */
.react-flow__node {
  pointer-events: all !important;
  touch-action: none !important;
  cursor: move !important;
}

.react-flow__pane {
  pointer-events: all !important;
  touch-action: none !important;
  cursor: grab !important;
}

.react-flow__handle {
  pointer-events: all !important;
}

.react-flow__edge {
  pointer-events: all !important;
}

/* 自定义连接线样式 */
.custom-connection-line {
  stroke: #3b82f6;
  stroke-width: 3;
  stroke-dasharray: 5, 5;
}
