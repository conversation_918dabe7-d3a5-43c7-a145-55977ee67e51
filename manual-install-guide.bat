@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
title ChatDB 手动安装指南

color 0E

echo.
echo ╔════════════════════════════════════════╗
echo ║         ChatDB 手动安装指南            ║
echo ║       当自动安装失败时使用此指南       ║
echo ╚════════════════════════════════════════╝
echo.

:: 检查当前环境
echo ┌─ 当前环境检查 ─┐
echo.

:: 检查Python
echo [检查] Python 状态...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] Python 未安装或未添加到PATH
    set "NEED_PYTHON=1"
) else (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        echo [✅] Python %%v 已安装
        set "PYTHON_VER=%%v"
        
        :: 检查版本
        echo %%v | findstr /r "^3\.[9-9]\|^3\.1[0-9]\|^[4-9]\." >nul
        if %errorlevel% neq 0 (
            echo [⚠️] 版本过低，建议升级到 3.9+
            set "NEED_PYTHON=1"
        ) else (
            echo [✅] 版本满足要求
            set "NEED_PYTHON=0"
        )
    )
)

:: 检查Node.js
echo [检查] Node.js 状态...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] Node.js 未安装或未添加到PATH
    set "NEED_NODEJS=1"
) else (
    for /f %%v in ('node --version 2^>^&1') do (
        echo [✅] Node.js %%v 已安装
        set "NODE_VER=%%v"
        
        :: 检查版本
        echo %%v | findstr /r "^v1[6-9]\.\|^v[2-9][0-9]\." >nul
        if %errorlevel% neq 0 (
            echo [⚠️] 版本过低，建议升级到 16+
            set "NEED_NODEJS=1"
        ) else (
            echo [✅] 版本满足要求
            set "NEED_NODEJS=0"
        )
    )
)

:: 检查pip
echo [检查] pip 状态...
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] pip 未安装或不可用
) else (
    echo [✅] pip 可用
)

:: 检查npm
echo [检查] npm 状态...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [❌] npm 未安装或不可用
) else (
    echo [✅] npm 可用
)

echo.
echo └─ 环境检查完成 ─┘
echo.

:: 显示安装指南
echo ╔════════════════════════════════════════╗
echo ║              手动安装指南              ║
echo ╚════════════════════════════════════════╝
echo.

if !NEED_PYTHON! == 1 (
    echo ┌─ Python 3.9+ 安装步骤 ─┐
    echo.
    echo 📥 下载地址:
    echo    https://www.python.org/downloads/
    echo.
    echo 📋 安装步骤:
    echo    1. 点击上方链接下载最新版本 Python
    echo    2. 运行下载的安装程序
    echo    3. ⚠️  重要: 勾选 "Add Python to PATH"
    echo    4. 选择 "Install Now" 或自定义安装
    echo    5. 等待安装完成
    echo.
    echo 🔧 验证安装:
    echo    打开新的命令行窗口，输入: python --version
    echo.
    echo 🌐 国内用户建议:
    echo    下载地址: https://registry.npmmirror.com/binary.html?path=python/
    echo.
    echo └─ Python 安装指南 ─┘
    echo.
)

if !NEED_NODEJS! == 1 (
    echo ┌─ Node.js 16+ 安装步骤 ─┐
    echo.
    echo 📥 下载地址:
    echo    https://nodejs.org/
    echo.
    echo 📋 安装步骤:
    echo    1. 点击上方链接，选择 LTS 版本下载
    echo    2. 运行下载的 .msi 安装程序
    echo    3. 按照向导完成安装 (默认设置即可)
    echo    4. 安装程序会自动添加到 PATH
    echo.
    echo 🔧 验证安装:
    echo    打开新的命令行窗口，输入:
    echo    - node --version
    echo    - npm --version
    echo.
    echo 🌐 国内用户建议:
    echo    下载地址: https://registry.npmmirror.com/binary.html?path=node/
    echo.
    echo └─ Node.js 安装指南 ─┘
    echo.
)

:: 如果都已安装
if !NEED_PYTHON! == 0 if !NEED_NODEJS! == 0 (
    echo ╔════════════════════════════════════════╗
    echo ║            环境已满足要求！            ║
    echo ╚════════════════════════════════════════╝
    echo.
    echo [✅] Python 和 Node.js 都已正确安装
    echo [🚀] 可以直接运行 START.bat 启动 ChatDB
    echo.
    
    set /p "start_now=是否现在启动 ChatDB? (Y/N): "
    if /i "!start_now!"=="Y" (
        echo [启动] 正在启动 ChatDB...
        call START.bat
        exit /b 0
    )
)

:: 通用故障排除
echo ╔════════════════════════════════════════╗
echo ║              故障排除                  ║
echo ╚════════════════════════════════════════╝
echo.

echo 🔧 常见问题解决:
echo.
echo ❓ 问题: 安装后命令不可用
echo 💡 解决:
echo    1. 重新打开命令行窗口
echo    2. 重启计算机
echo    3. 检查环境变量 PATH 设置
echo.
echo ❓ 问题: Python 安装后 pip 不可用
echo 💡 解决:
echo    1. 重新安装 Python，确保勾选 pip
echo    2. 手动安装 pip: python -m ensurepip --upgrade
echo.
echo ❓ 问题: 网络下载失败
echo 💡 解决:
echo    1. 使用国内镜像站下载
echo    2. 关闭防火墙/杀毒软件重试
echo    3. 使用手机热点网络
echo.
echo ❓ 问题: 权限不足
echo 💡 解决:
echo    1. 以管理员身份运行安装程序
echo    2. 临时关闭 UAC (用户账户控制)
echo.

echo ╔════════════════════════════════════════╗
echo ║              快速链接                  ║
echo ╚════════════════════════════════════════╝
echo.
echo 🔗 官方下载:
echo    Python:  https://www.python.org/downloads/
echo    Node.js: https://nodejs.org/
echo.
echo 🔗 国内镜像:
echo    Python:  https://registry.npmmirror.com/binary.html?path=python/
echo    Node.js: https://registry.npmmirror.com/binary.html?path=node/
echo.
echo 🔗 在线安装器:
echo    Chocolatey: https://chocolatey.org/install
echo    Scoop:      https://scoop.sh/
echo.

echo ╔════════════════════════════════════════╗
echo ║              操作选项                  ║
echo ╚════════════════════════════════════════╝
echo.
echo [1] 打开 Python 官网
echo [2] 打开 Node.js 官网
echo [3] 重新检查环境
echo [4] 启动 ChatDB (如果环境已就绪)
echo [5] 退出
echo.

set /p "choice=请选择操作 (1-5): "

if "%choice%"=="1" (
    start https://www.python.org/downloads/
    echo [打开] Python 官网
)
if "%choice%"=="2" (
    start https://nodejs.org/
    echo [打开] Node.js 官网
)
if "%choice%"=="3" (
    echo [重新检查] 环境状态...
    call "%~f0"
    exit /b 0
)
if "%choice%"=="4" (
    if !NEED_PYTHON! == 0 if !NEED_NODEJS! == 0 (
        echo [启动] ChatDB...
        call START.bat
    ) else (
        echo [错误] 环境尚未满足要求，请先安装必要软件
    )
)
if "%choice%"=="5" (
    echo [退出] 再见！
    exit /b 0
)

echo.
pause
