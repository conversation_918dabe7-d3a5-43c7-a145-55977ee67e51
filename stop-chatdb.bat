@echo off
chcp 65001 >nul
echo ========================================
echo    ChatDB 服务停止脚本 (Windows)
echo ========================================
echo.

:: 设置颜色
color 0C

echo [INFO] 正在停止 ChatDB 相关服务...
echo.

:: 停止后端服务 (FastAPI/uvicorn)
echo [1/3] 停止后端服务...
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq ChatDB Backend*" /fo table /nh 2^>nul') do (
    if not "%%i"=="INFO:" (
        echo [INFO] 发现后端进程: %%i
        taskkill /pid %%i /f >nul 2>&1
        if %errorLevel% == 0 (
            echo [✓] 后端进程已停止
        )
    )
)

:: 通过端口停止后端服务
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8000 ^| findstr LISTENING') do (
    echo [INFO] 停止占用8000端口的进程: %%a
    taskkill /pid %%a /f >nul 2>&1
)

:: 停止前端服务 (React/Node.js)
echo [2/3] 停止前端服务...
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq ChatDB Frontend*" /fo table /nh 2^>nul') do (
    if not "%%i"=="INFO:" (
        echo [INFO] 发现前端进程: %%i
        taskkill /pid %%i /f >nul 2>&1
        if %errorLevel% == 0 (
            echo [✓] 前端进程已停止
        )
    )
)

:: 通过端口停止前端服务
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
    echo [INFO] 停止占用3000端口的进程: %%a
    taskkill /pid %%a /f >nul 2>&1
)

:: 停止相关的Python和Node进程
echo [3/3] 清理相关进程...

:: 停止可能的Python进程（谨慎操作）
for /f "tokens=2,9" %%a in ('tasklist /fo csv /v ^| findstr /i "python.*main.py\|uvicorn.*main:app"') do (
    echo [INFO] 停止Python进程: %%a
    taskkill /pid %%a /f >nul 2>&1
)

:: 停止可能的Node进程（谨慎操作）
for /f "tokens=2,9" %%a in ('tasklist /fo csv /v ^| findstr /i "node.*react-scripts"') do (
    echo [INFO] 停止Node进程: %%a
    taskkill /pid %%a /f >nul 2>&1
)

echo.
echo [✓] 服务停止完成
echo.

:: 检查端口是否已释放
echo [INFO] 检查端口状态...
netstat -an | findstr :8000 >nul 2>&1
if %errorLevel% == 0 (
    echo [!] 端口8000仍被占用
) else (
    echo [✓] 端口8000已释放
)

netstat -an | findstr :3000 >nul 2>&1
if %errorLevel% == 0 (
    echo [!] 端口3000仍被占用
) else (
    echo [✓] 端口3000已释放
)

echo.
echo ========================================
echo           停止完成！
echo ========================================
echo.
pause
