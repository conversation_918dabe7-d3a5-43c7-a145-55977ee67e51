@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
title ChatDB 停止脚本

echo ========================================
echo    ChatDB 停止脚本 (Windows)
echo ========================================
echo.

echo [INFO] 正在停止ChatDB服务...

:: 停止端口8000的进程（后端）
echo [INFO] 停止后端服务（端口8000）...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
    set "pid=%%a"
    if "!pid!" neq "" (
        echo [INFO] 终止进程 !pid!
        taskkill /pid !pid! /f >nul 2>&1
    )
)

:: 停止端口3000的进程（前端）
echo [INFO] 停止前端服务（端口3000）...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
    set "pid=%%a"
    if "!pid!" neq "" (
        echo [INFO] 终止进程 !pid!
        taskkill /pid !pid! /f >nul 2>&1
    )
)

:: 停止命名窗口
echo [INFO] 停止命名窗口...
taskkill /fi "WindowTitle eq ChatDB-Backend*" /f >nul 2>&1
taskkill /fi "WindowTitle eq ChatDB-Frontend*" /f >nul 2>&1

:: 检查结果
echo.
echo [INFO] 检查端口状态...
netstat -ano | findstr :8000 >nul 2>&1
if errorlevel 1 (
    echo [OK] 端口8000已释放
) else (
    echo [WARN] 端口8000仍被占用
)

netstat -ano | findstr :3000 >nul 2>&1
if errorlevel 1 (
    echo [OK] 端口3000已释放
) else (
    echo [WARN] 端口3000仍被占用
)

echo.
echo ========================================
echo           停止完成！
echo ========================================
echo.
pause
