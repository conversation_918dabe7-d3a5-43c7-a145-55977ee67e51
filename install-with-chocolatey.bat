@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul 2>&1
title ChatDB 环境安装器 (Chocolatey版)

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo ╔════════════════════════════════════════╗
    echo ║              权限不足！                ║
    echo ╚════════════════════════════════════════╝
    echo.
    echo [错误] 此脚本需要管理员权限
    echo [解决] 请右键点击此脚本，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

color 0D

echo.
echo ╔════════════════════════════════════════╗
echo ║    ChatDB 环境安装器 (Chocolatey版)    ║
echo ║      使用 Chocolatey 包管理器安装      ║
echo ╚════════════════════════════════════════╝
echo.

:: 检查网络连接
echo [检查] 网络连接...
ping -n 1 chocolatey.org >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 无法连接到 Chocolatey 服务器
    echo [建议] 请检查网络连接或使用 install-environment.bat
    pause
    exit /b 1
)
echo [成功] 网络连接正常
echo.

:: 检查Chocolatey
echo [检查] Chocolatey 安装状态...
choco --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [安装] Chocolatey 包管理器...
    
    :: 安装Chocolatey
    powershell -NoProfile -InputFormat None -ExecutionPolicy Bypass -Command "& {[System.Net.ServicePointManager]::SecurityProtocol = 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))}"
    
    if %errorlevel% neq 0 (
        echo [错误] Chocolatey 安装失败
        pause
        exit /b 1
    )
    
    :: 刷新环境变量
    call refreshenv
    echo [成功] Chocolatey 安装完成
) else (
    echo [跳过] Chocolatey 已安装
)
echo.

:: 检查现有软件
echo ┌─ 环境检查 ─┐
echo.

set "NEED_PYTHON=0"
set "NEED_NODEJS=0"

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [需要] Python 未安装
    set "NEED_PYTHON=1"
) else (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        echo [发现] Python %%v
        :: 简单版本检查
        echo %%v | findstr /r "^3\.[9-9]\|^3\.1[0-9]\|^[4-9]\." >nul
        if %errorlevel% neq 0 (
            echo [需要] Python 版本需要升级到 3.9+
            set "NEED_PYTHON=1"
        )
    )
)

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [需要] Node.js 未安装
    set "NEED_NODEJS=1"
) else (
    for /f %%v in ('node --version 2^>^&1') do (
        echo [发现] Node.js %%v
        :: 检查版本 (v16+)
        echo %%v | findstr /r "^v1[6-9]\.\|^v[2-9][0-9]\." >nul
        if %errorlevel% neq 0 (
            echo [需要] Node.js 版本需要升级到 16+
            set "NEED_NODEJS=1"
        )
    )
)

echo.
echo └─ 环境检查完成 ─┘
echo.

:: 如果都满足要求
if !NEED_PYTHON! == 0 if !NEED_NODEJS! == 0 (
    echo ╔════════════════════════════════════════╗
    echo ║            环境已满足要求！            ║
    echo ╚════════════════════════════════════════╝
    echo.
    pause
    exit /b 0
)

:: 显示安装计划
echo ╔════════════════════════════════════════╗
echo ║              安装计划                  ║
echo ╚════════════════════════════════════════╝
echo.
if !NEED_PYTHON! == 1 echo [计划] 使用 Chocolatey 安装 Python 3
if !NEED_NODEJS! == 1 echo [计划] 使用 Chocolatey 安装 Node.js LTS
echo.

set /p "confirm=确认开始安装? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo [取消] 用户取消安装
    pause
    exit /b 0
)

echo.

:: 安装Python
if !NEED_PYTHON! == 1 (
    echo ┌─ 安装 Python ─┐
    echo.
    echo [安装] 正在通过 Chocolatey 安装 Python...
    choco install python -y
    if %errorlevel% neq 0 (
        echo [错误] Python 安装失败
        pause
        exit /b 1
    )
    echo [成功] Python 安装完成
    echo.
    echo └─ Python 安装完成 ─┘
    echo.
)

:: 安装Node.js
if !NEED_NODEJS! == 1 (
    echo ┌─ 安装 Node.js ─┐
    echo.
    echo [安装] 正在通过 Chocolatey 安装 Node.js...
    choco install nodejs -y
    if %errorlevel% neq 0 (
        echo [错误] Node.js 安装失败
        pause
        exit /b 1
    )
    echo [成功] Node.js 安装完成
    echo.
    echo └─ Node.js 安装完成 ─┘
    echo.
)

:: 刷新环境变量
echo [刷新] 环境变量...
call refreshenv

:: 验证安装
echo ┌─ 安装验证 ─┐
echo.

timeout /t 3 /nobreak >nul

echo [验证] Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] Python 命令不可用，可能需要重新打开命令行
) else (
    for /f "tokens=2" %%v in ('python --version 2^>^&1') do (
        echo [成功] Python %%v
    )
)

echo [验证] Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] Node.js 命令不可用，可能需要重新打开命令行
) else (
    for /f %%v in ('node --version 2^>^&1') do (
        echo [成功] Node.js %%v
    )
)

echo [验证] npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [警告] npm 命令不可用
) else (
    for /f %%v in ('npm --version 2^>^&1') do (
        echo [成功] npm %%v
    )
)

echo.
echo └─ 验证完成 ─┘
echo.

:: 完成
echo ╔════════════════════════════════════════╗
echo ║              安装完成！                ║
echo ╚════════════════════════════════════════╝
echo.
echo ✅ 安装摘要:
echo    - 使用 Chocolatey 包管理器
if !NEED_PYTHON! == 1 echo    - Python 已安装
if !NEED_NODEJS! == 1 echo    - Node.js 已安装
echo    - 环境变量已配置
echo.
echo 🚀 下一步:
echo    1. 重新打开命令行窗口
echo    2. 运行 START.bat 启动 ChatDB
echo.
echo 💡 Chocolatey 优势:
echo    - 自动管理软件更新: choco upgrade all
echo    - 轻松卸载软件: choco uninstall python
echo    - 查看已安装软件: choco list --local-only
echo.

pause
